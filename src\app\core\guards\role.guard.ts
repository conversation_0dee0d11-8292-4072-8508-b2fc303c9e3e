import { Injectable } from '@angular/core';
import {
  Router,
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../services/auth/auth.service';
import { Role } from '../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class RoleGuard implements CanActivate {
  constructor(private router: Router, private authService: AuthService) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const user = this.authService.getCurrentUser();

    if (!user) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check if route has data.roles specified
    const requiredRoles = route.data['roles'] as Role[];

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No specific roles required
    }

    // Check if user has roles and at least one of the required roles
    const hasRequiredRole = user.roles?.some((role) =>
      requiredRoles.includes(role)
    ) ?? false;

    if (hasRequiredRole) {
      return true;
    }

    // User doesn't have the required role, redirect to dashboard
    this.router.navigate(['/dashboard']);
    return false;
  }
}
