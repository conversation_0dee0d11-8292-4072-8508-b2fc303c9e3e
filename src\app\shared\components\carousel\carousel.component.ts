import { Component, HostBinding, Input,} from '@angular/core';

@Component({
  selector: 'app-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.css']
})
export class CarouselComponent{
  @Input() slides: { content: string }[] = [];
  @Input() dotColor: string = '#ffffff';
  @Input() dotColorActive: string = '#ffffff';
  @Input() carouselWidth: string = '60%';
  @Input() minHeight: string = '500px';

  carouselItems = [
    { src: "assets/ht.png", alt: "HT", bg: "#01B0CD" },
    { src: "assets/mint.png", alt: "Mint", bg: "#F99E1C" },
    { src: "assets/prmantra.png", alt: "PRmantra", bg: "#FFFFFF" },
    { src: "assets/paisabazaar.png", alt: "<PERSON><PERSON>", bg: "#FFFFFF" },
    { src: "assets/inc42.png", alt: "Inc42", bg: "#FFFFFF" },
    { src: "assets/entrackr.png", alt: "Entrackr", bg: "#FFFFFF" }
  ];
 

  @HostBinding('style.--min-height') get getMinHeight() {
    return this.minHeight;
  }

  @HostBinding('style.--dot-color') get getDotColor() {
    return this.dotColor;
  }

  @HostBinding('style.--dot-color-active') get getDotColorActive() {
    return this.dotColorActive;
  }

  @HostBinding('style.--carousel-width') get getCarouselWidth() {
    return this.carouselWidth;
  }

  slideConfig = {
    slidesToShow: 1, // Number of visible slides
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    pauseOnHover: true,
    dots: true, 
    infinite: true,
    // arrows: true, 
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          arrows: true,
          infinite: true,
          slidesToShow: 1,
          slidesToScroll: 1,
          dots: true
        }
      },
      {
        breakpoint: 768,
        settings: {
          arrows: true,
          infinite: true,
          slidesToShow: 1,
          slidesToScroll: 1,
          dots: true
        }
      }
    ]
  };

}
