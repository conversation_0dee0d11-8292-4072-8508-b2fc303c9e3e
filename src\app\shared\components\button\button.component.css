.ripple-btn {
  @apply inline-flex font-[Inter] items-center justify-center font-[Inter] font-normal transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:opacity-80;
}

.ripple-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btnBlue {
  background: linear-gradient(270deg, #0c023e 0%, #0f0253 100%);
}

.custom-btn:hover {
  background: linear-gradient(270deg, #f42346 0%, #f5327d 100%);
}

.btn-outline {
  @apply bg-backgroundPrimary border-2;
}

.btn-outline-primary {
  @apply border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white;
}

.btn-outline-secondary {
  @apply border-gray-500 text-gray-500 hover:bg-gray-500 hover:text-white;
}

.btn-outline-success {
  @apply border-green-500 text-green-500 hover:bg-green-500 hover:text-white;
}

.btn-outline-error {
  @apply border-red-500 text-red-500 hover:bg-red-500 hover:text-white;
}

.btn-outline-warning {
  @apply border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white;
}

.btn-outline-gradient {
  border-width: 2px;
  border-style: solid;
  border-image-source: linear-gradient(270deg, #f42346 0%, #f5327d 100%);
  border-image-slice: 1;
  color: #f42346;
}

.btn-outline-customGradient {
  border-width: 2px;
  border-style: solid;
  border-image-source: linear-gradient(270deg, #0c023e 0%, #0f0253 100%);
  border-image-slice: 1;
  color: #0b013d;
}

.btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}
.btn-secondary {
  @apply bg-gray-500 text-white hover:bg-gray-600;
}
.btn-success {
  @apply bg-green-500 text-white hover:bg-green-600;
}
.btn-error {
  @apply bg-red-500 text-white hover:bg-red-600;
}
.btn-warning {
  @apply bg-yellow-500 text-white hover:bg-yellow-600;
}
.btn-spotlight {
  @apply bg-gray-200 text-[#F3254D] hover:bg-white;
}

.btn-gradient {
  @apply bg-gradient-to-r from-pink-500 to-red-500 text-white;
}

.btn-gradient:focus-visible {
  outline-color: rgb(var(--error));
}

.btn-customGradient {
  @apply bg-gradient-to-r from-[#0B013D] to-[#342c52] text-white;
}

.btn-rounded {
  @apply rounded-full;
}

.btn-square {
  @apply rounded-md;
}

.btn-block {
  @apply w-full;
}
