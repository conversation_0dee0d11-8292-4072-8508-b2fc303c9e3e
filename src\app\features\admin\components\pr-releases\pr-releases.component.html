<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <div class="flex items-center gap-2">
      <h1 class="text-2xl font-bold">PR Releases</h1>
      <button class="ml-2 text-gray-600 hover:text-gray-800">
        <i class="fas fa-question-circle"></i>
      </button>
    </div>
    <button class="bg-[#FF4D8D] text-white px-6 py-2 rounded-full flex items-center gap-2">
      <i class="fas fa-plus"></i>
      <span>PR Release</span>
    </button>
  </div>

  <!-- Filters -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <!-- Type Filter -->
    <div class="bg-[#F3F1FF] p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-4">Filter by Type</h3>
      <div class="space-y-3">
        <div *ngFor="let type of filterTypes" class="flex items-center">
          <input
            type="checkbox"
            [id]="type.id"
            [checked]="selectedType === type.id"
            class="form-checkbox h-5 w-5 text-[#FF4D8D]"
          >
          <label [for]="type.id" class="ml-2 text-gray-700">{{ type.label }}</label>
        </div>
      </div>
    </div>

    <!-- Industry Filter -->
    <div class="bg-[#F3F1FF] p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-4">Filter by Industry</h3>
      <div class="space-y-3">
        <div *ngFor="let industry of filterIndustries" class="flex items-center">
          <input
            type="checkbox"
            [id]="industry.id"
            class="form-checkbox h-5 w-5 text-[#FF4D8D]"
          >
          <label [for]="industry.id" class="ml-2 text-gray-700">{{ industry.label }}</label>
        </div>
      </div>
    </div>

    <!-- Status Filter -->
    <div class="bg-[#F3F1FF] p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-4">Filter by Status</h3>
      <div class="space-y-3">
        <div *ngFor="let status of filterStatus" class="flex items-center">
          <input
            type="checkbox"
            [id]="status.id"
            class="form-checkbox h-5 w-5 text-[#FF4D8D]"
          >
          <label [for]="status.id" class="ml-2 text-gray-700">{{ status.label }}</label>
        </div>
      </div>
    </div>
  </div>

  <!-- Search Bar -->
  <div class="relative mb-8">
    <input
      type="text"
      placeholder="Search PR topics, companies, or keywords..."
      class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF4D8D] focus:border-transparent"
    >
    <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
    <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
      <i class="fas fa-sliders-h text-gray-600"></i>
    </button>
  </div>

  <!-- PR Releases List -->
  <div class="space-y-6">
    <div *ngFor="let release of releases" class="bg-white rounded-xl shadow-sm p-6 grid grid-cols-2">
      <div class="flex items-start justify-between">
        <div class="flex items-start gap-4">
          <img [src]="release.logo" [alt]="release.company" class="w-12 h-12 rounded-lg">
          <div>
            <div class="flex items-center gap-2">
              <h3 class="text-lg font-semibold">{{ release.company }}</h3>
              <span class="text-sm text-gray-500">{{ release.industry }}</span>
              <span *ngIf="release.isNew" class="px-2 py-1 text-xs bg-[#FF4D8D] text-white rounded-full">NEW</span>
            </div>
            <h4 class="text-xl font-bold mt-2">{{ release.title }}</h4>
            <p class="text-gray-600 mt-2">{{ release.description }}</p>
            <div class="flex gap-2 mt-4">
              <span *ngFor="let tag of release.tags" 
                    class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                {{ tag }}
              </span>
            </div>
            <div class="flex items-center gap-4 mt-4 text-sm text-gray-500">
              <span>Posted: {{ release.posted }}</span>
              <span>Deadline: {{ release.deadline }}</span>
            </div>
          </div>
        </div>
        <div class="flex gap-2">
          <button class="p-2 hover:bg-gray-100 rounded-full">
            <i class="far fa-bookmark"></i>
          </button>
          <button class="p-2 hover:bg-gray-100 rounded-full">
            <i class="fas fa-share-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div> 