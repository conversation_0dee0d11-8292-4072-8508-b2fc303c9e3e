import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

interface BillingInfo {
  name: string;
  email: string;
  companyName: string;
  address: string;
}

interface PaymentMethod {
  id: string;
  number: string;
  expiry: string;
  type: string;
}

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}

@Component({
  selector: 'app-billing-settings',
  templateUrl: './billing-settings.component.html',
  styleUrls: ['./billing-settings.component.css']
})
export class BillingSettingsComponent implements OnInit {
  activeTab: 'overview' | 'credit-activity' | 'invoices' = 'overview';
  credits: number = 500;
  
  billingInfo: BillingInfo = {
    name: '<PERSON>',
    email: '<EMAIL>',
    companyName: 'Acme Corp',
    address: '123 Market Street, SF, CA 94103'
  };

  paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      number: '**** **** **** 1234 (Visa)',
      expiry: '08/27',
      type: 'visa'
    }
  ];

  constructor(private router: Router) { }

  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png'
  };
  ngOnInit(): void {
    // Initialize component data
  }

  setActiveTab(tab: 'overview' | 'credit-activity' | 'invoices'): void {
    this.activeTab = tab;
  }

  addMoreCredits(): void {
    // Implement add credits logic
    console.log('Adding more credits');
  }

  editBillingInfo(): void {
    // Implement edit billing info logic
    console.log('Editing billing info', this.billingInfo);
  }

  addPaymentMethod(): void {
    // Implement add payment method logic
    console.log('Adding new payment method');
  }

  deleteCard(cardId: string): void {
    this.paymentMethods = this.paymentMethods.filter(card => card.id !== cardId);
    console.log('Deleted card', cardId);
  }

  goBack(): void {
    this.router.navigate(['/dashboard/settings']);
  }

   events=[1,2]
   TeamMembers=[1,2,3,4,5,6]

} 