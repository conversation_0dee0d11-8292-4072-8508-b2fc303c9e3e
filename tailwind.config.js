/* @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,ts}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#0B013D",
          50: "#ececff",
          100: "#D0C5F5",
          200: "#A38EEB",
          300: "#6850C4",
          400: "#37248A",
          500: "#0B013D",
          600: "#080034",
          700: "#06002B",
          800: "#040022",
          900: "#020019",
        },
        secondary: {
          DEFAULT: "#0A0628",
          50: "#2A1E8C",
          100: "#251A7A",
          200: "#1F1668",
          300: "#191256",
          400: "#130E44",
          500: "#0A0628",
          600: "#080520",
          700: "#060418",
          800: "#040310",
          900: "#020208",
        },
        accent: {
          DEFAULT: "#4B3B89",
          50: "#9A8AD8",
          100: "#8D7BD3",
          200: "#735EC8",
          300: "#5C45B6",
          400: "#4B3B89",
          500: "#3A2F7D",
          600: "#2A2258",
          700: "#1A1534",
          800: "#0A0810",
          900: "#000000",
        },
        success: {
          DEFAULT: "",
          50: "#E6F9F4",
          100: "#C0F2E9",
          200: "#99EBE3",
          300: "#73E4DD",
          400: "#13DE9B",
          500: "#2CCE8A",
          600: "#29B97E",
          700: "#26A472",
          800: "#238F66",
          900: "#000000",
        },
        warning: {
          DEFAULT: "#FB6340",
          50: "#FEE4D9",
          100: "#FDD0B3",
          200: "#FDBB8D",
          300: "#FC9F67",
          400: "#F7AE32",
          500: "#FB6340",
          600: "#F9451D",
          700: "#F7270A",
          800: "#C51F08",
          900: "#941706",
        },
        danger: {
          DEFAULT: "#DC2626",
          50: "#FEE4E9",
          100: "#FDD0D5",
          200: "#FDBBBE",
          300: "#FC97A7",
          400: "#F26A6A",
          500: "#DC2626",
          600: "#E71414",
          700: "#B30F0F",
          800: "#800B0B",
          900: "#4C0707",
        },
        info: {
          DEFAULT: "#0ECDEF",
          50: "#EBF2FE",
          100: "#D7E6FD",
          200: "#B0CDFB",
          300: "#89B4FA",
          400: "#629BF8",
          500: "#0ECDEF",
          600: "#0B61EE",
          700: "#084BB8",
          800: "#063583",
          900: "#041F4D",
        },
        subtitle: {
          DEFAULT: "#777777",
        },
        subtitlelight: {
          DEFAULT: "#F2F4F7",
        },
        badgebg: {
          DEFAULT: "#F2F2F2",
        },
        black1: {
          DEFAULT: "#09071C",
        },
        lightBlack: {
          DEFAULT: "#11181C",
        },
      },
      backgroundImage: {
        "gradient-primary":
          "linear-gradient(90deg, #F42346 0%, #F5327D 56.29%)",
        "gradient-secondary":
          "linear-gradient(90deg, #0B013D 0%, #3B3363 51%, #0C023D 100%)",
        "gradient-tertiary":
          "radial-gradient(50% 50% at 100% 0%, #0B013D 0%, rgba(11, 1, 61, 0.85098) 100%)",
        "gradient-primary-vertical":
          "linear-gradient(to bottom, var(--tw-gradient-stops))",
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      gradientColorStops: {
        "primary-start": "#F42346",
        "primary-end": "#F5327D",
        "secondary-start": "#0B013D",
        "secondary-mid": "#3B3363",
        "secondary-end": "#0C023D",
        "tertiary-start": "#0B013D",
        "tertiary-end": "rgba(11, 1, 61, 0.85098)",
        "accent-start": "#4B3B89",
        "accent-end": "#2A2258",
        "success-start": "#2CCE8A",
        "success-end": "#238F66",
        "warning-start": "#FB6340",
        "warning-end": "#F9451D",
        "danger-start": "#F5365B",
        "danger-end": "#E71414",
        "info-start": "#0ECDEF",
        "info-end": "#0B61EE",
      },
    },
    fontFamily: {
      inter: ["Inter", "sans-serif"],
    },
  },
  plugins: [require("rippleui")],
  rippleui: {
    removeThemes: ["dark"],
  },
};
