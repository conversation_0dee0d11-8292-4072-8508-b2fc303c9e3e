import { Component, OnInit } from '@angular/core';

interface Person {
  image: string;
  name: string;
  title: string;
  publications: string;
  covers: string[];
  phone: string;
  email: string;
  location: string;
}

interface Company {
  logo: string;
  name: string;
  industry: string;
  website: string;
  size: string;
  founded: string;
  headquarters: string;
  description: string;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

@Component({
  selector: 'app-media-data',
  templateUrl: './media-data.component.html',
  styleUrls: ['./media-data.component.css']
})
export class MediaDataComponent implements OnInit {
  activeView: 'people' | 'companies' = 'people';
  searchTerm: string = '';
  
  // Original data arrays
  private _people: Person[] = [
    {
      image: 'assets/images/avatars/avatar-1.jpg',
      name: '<PERSON>',
      title: 'Reporter',
      publications: 'Law360',
      covers: ['lawyer', 'public policy', 'automotive industry', 'law360', 'law', 'infrastructure'],
      phone: '+44 **********',
      email: '<EMAIL>',
      location: 'Leeds, UK'
    },
    {
      image: 'assets/images/avatars/avatar-2.jpg',
      name: '<PERSON>',
      title: 'Journalist',
      publications: 'Apparel Insider',
      covers: ['sustainability', 'fashion', 'textile', 'retail', 'clothing industry', 'fashion design'],
      phone: '+44 **********',
      email: '<EMAIL>',
      location: 'Leeds, UK'
    },
    {
      image: 'assets/images/avatars/avatar-3.jpg',
      name: 'Raphael Garcia',
      title: 'Reporter',
      publications: 'Foreign Policy, VICE, salon.com, opendemocracy.net, onecount.net',
      covers: ['social media', 'president of the united states', 'twitter', 'conservatism', 'society'],
      phone: '+44 **********',
      email: '<EMAIL>',
      location: 'Leeds, UK'
    },
    {
      image: 'assets/images/avatars/avatar-4.jpg',
      name: 'Anushka Basu',
      title: 'Reporter',
      publications: 'crypto.news',
      covers: ['bitcoin', 'cryptocurrency', 'blockchain', 'economics', 'digital currency', 'investment'],
      phone: '+44 **********',
      email: '<EMAIL>',
      location: 'Leeds, UK'
    }
  ];

  private _companies: Company[] = [
    {
      logo: 'assets/images/companies/company-1.jpg',
      name: 'TechCorp Solutions',
      industry: 'Technology',
      website: 'www.techcorp.com',
      size: '1000-5000',
      founded: '2010',
      headquarters: 'San Francisco, USA',
      description: 'Leading provider of enterprise software solutions',
      socialLinks: {
        linkedin: 'https://linkedin.com/techcorp',
        twitter: 'https://twitter.com/techcorp'
      }
    },
    {
      logo: 'assets/images/companies/company-2.jpg',
      name: 'GreenEnergy Inc',
      industry: 'Renewable Energy',
      website: 'www.greenenergy.com',
      size: '500-1000',
      founded: '2015',
      headquarters: 'Berlin, Germany',
      description: 'Sustainable energy solutions for a better future',
      socialLinks: {
        linkedin: 'https://linkedin.com/greenenergy',
        twitter: 'https://twitter.com/greenenergy'
      }
    },
    {
      logo: 'assets/images/companies/company-3.jpg',
      name: 'HealthPlus Medical',
      industry: 'Healthcare',
      website: 'www.healthplus.com',
      size: '5000+',
      founded: '2005',
      headquarters: 'London, UK',
      description: 'Innovative healthcare technology and services',
      socialLinks: {
        linkedin: 'https://linkedin.com/healthplus',
        twitter: 'https://twitter.com/healthplus'
      }
    },
    {
      logo: 'assets/images/companies/company-4.jpg',
      name: 'FinTech Innovations',
      industry: 'Financial Technology',
      website: 'www.fintechinno.com',
      size: '100-500',
      founded: '2018',
      headquarters: 'Singapore',
      description: 'Next-generation financial technology solutions',
      socialLinks: {
        linkedin: 'https://linkedin.com/fintech',
        twitter: 'https://twitter.com/fintech'
      }
    }
  ];

  // Filtered data getters
  get people(): Person[] {
    return this.searchTerm
      ? this._people.filter(person => 
          this.matchesPeopleSearch(person, this.searchTerm.toLowerCase())
        )
      : this._people;
  }

  get companies(): Company[] {
    return this.searchTerm
      ? this._companies.filter(company => 
          this.matchesCompanySearch(company, this.searchTerm.toLowerCase())
        )
      : this._companies;
  }

  constructor() { }

  ngOnInit(): void {
  }

  switchView(view: 'people' | 'companies'): void {
    this.activeView = view;
  }

  onSearch(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value;
  }

  private matchesPeopleSearch(person: Person, searchTerm: string): boolean {
    return (
      person.name.toLowerCase().includes(searchTerm) ||
      person.title.toLowerCase().includes(searchTerm) ||
      person.publications.toLowerCase().includes(searchTerm) ||
      person.location.toLowerCase().includes(searchTerm) ||
      person.covers.some(topic => topic.toLowerCase().includes(searchTerm)) ||
      person.email.toLowerCase().includes(searchTerm)
    );
  }

  private matchesCompanySearch(company: Company, searchTerm: string): boolean {
    return (
      company.name.toLowerCase().includes(searchTerm) ||
      company.industry.toLowerCase().includes(searchTerm) ||
      company.website.toLowerCase().includes(searchTerm) ||
      company.size.toLowerCase().includes(searchTerm) ||
      company.headquarters.toLowerCase().includes(searchTerm) ||
      company.description.toLowerCase().includes(searchTerm)
    );
  }

  downloadCSV(): void {
    console.log('Downloading CSV...');
  }

  uploadData(): void {
    console.log('Uploading data...');
  }

  viewProfile(item: Person | Company): void {
    console.log('Viewing profile:', item.name);
  }
} 