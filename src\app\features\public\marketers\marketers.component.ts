import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import gsap from 'gsap';
import { Router, NavigationEnd } from '@angular/router';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-marketers',
  templateUrl: './marketers.component.html',
  styleUrls: ['./marketers.component.css']
})
export class MarketersComponent implements OnInit, AfterViewInit {
  @ViewChild('heroText', { static: false }) heroText!: ElementRef;
    @ViewChild('parallaxImage', { static: false }) parallaxImage!: ElementRef;
    @ViewChild('parallaxText', { static: false }) parallaxText!: ElementRef;
    @ViewChild('features', { static: false }) features!: ElementRef;
  
    constructor(private router: Router) {}
  
    ngOnInit() {
      this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          window.scrollTo(0, 0); // Forces scroll to top on route change
        }
      });
    }
  
    workContent = [
      {
        title: "Craft Your Professional Profile",
        description: "Showcase your areas of expertise and existing media coverage, making it easy for storytellers to find you based on your specific knowledge domains.",
        image: "assets/craft-profile.png"
      },
      {
        title: "Access Real-Time Media Opportunities",
        description: "Receive tailored email alerts for media requests matching your profile and configured interests, connecting you with journalists seeking your exact expertise.",
        image: "assets/media-opportunities.png"
      },
      {
        title: "Engage Directly with Media Professionals",
        description: "CommsClub streamlines communication, allowing you to easily connect and build relationships with journalists covering your industry.",
        image: "assets/engage-media.png"
      },
      {
        title: "Secure Coverage and Establish Authority",
        description: "Gain media exposure to enhance your online discoverability, extend your brand’s reach, and elevate your reputation in the eyes of your target audience.",
        image: "assets/secure-coverage.png"
      }
    ];
    
    highlights = [
      {
        title: "Showcase Your Expertise",
        description: "Highlight your credentials, expertise, and experience to build credibility in your field.",
        image: "assets/showcase.png",
        gap: "gap-[49px]",
        textLeading: "leading-[29px]"
      },
      {
        title: "Expand Your Reach",
        description: "Get discovered by countless journalists and content creators seeking insights from professionals like you for their stories.",
        image: "assets/expand.png",
        gap: "gap-[35px]",
        textLeading: "leading-[30px]"
      },
      {
        title: "Comprehensive Support",
        description: "Our data-driven insights and dedicated online support will help you maximize your visibility and grow your business effectively.",
        image: "assets/support.png",
        gap: "gap-[40px]",
        textLeading: "leading-[29px]"
      },
      {
        title: "Attract Media Attention",
        description: "Build a stellar reputation on CommsClub through quality contributions and prompt responses, encouraging media professionals to regularly seek your expert opinion.",
        image: "assets/media.png",
        gap: "gap-[28.9px]",
        textLeading: "leading-[29px]"
      }
    ];
  
    categories = [
      "Business & Finance",
      "Technology & Innovation",
      "Health & Wellness",
      "Politics & Government",
      "Entertainment & Culture",
      "Science & Environment",
      "Sports & Recreation",
      "Education & Learning",
      "Lifestyle & Travel",
      "Opinion & Editorial",
      "Real Estate & Property",
      "Food & Beverage",
      "Automotive & Transportation",
      "Fashion & Beauty",
      "Social Issues & Activism",
    ];
  
    innerCarousel = [
      { content: `
      <div class="relative font-[Inter] mt-[100px] max-w-3xl mx-auto p-6 bg-[#0c052b] text-white rounded-[2vw] text-center shadow-lg">
      <!-- Quote Icon Top Left -->
        <div class="w-[65px] h-[65px] absolute -top-8 left-3 bg-pink-500 text-white rounded-full">
          <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto mt-[-7px] ml-[10px]">
        </div>
    
      <!-- Profile Image -->
        <div class="absolute w-[200px] h-[200px] -top-[100px] left-1/2 transform -translate-x-1/2">
          <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
        </div>
    
      <!-- Testimonial Content -->
        <p class="mt-24 text-[22px] font-normal">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
        </p>
    
      <!-- Author -->
        <div class="mt-[50px]">
          <p class="font-normal text-[25px]">Mr. John</p>
          <p class="text-[17px]">Barry Maher & Associates</p>
        </div>
    
      <!-- Quote Icon Bottom Right -->
        <div class="w-[65px] h-[65px] absolute -bottom-3 right-3 bg-pink-500 text-white rounded-full">
          <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto transform scale-x-[-1] scale-y-[-1] mt-[50px] ml-[23px]">
        </div>
      </div>
      `},
      { content: `
        <div class="relative font-[Inter] mt-[100px] max-w-3xl mx-auto p-6 bg-[#0c052b] text-white rounded-[2vw] text-center shadow-lg">
        <!-- Quote Icon Top Left -->
          <div class="w-[65px] h-[65px] absolute -top-8 left-3 bg-pink-500 text-white rounded-full">
            <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto mt-[-7px] ml-[10px]">
          </div>
      
        <!-- Profile Image -->
          <div class="absolute w-[200px] h-[200px] -top-[100px] left-1/2 transform -translate-x-1/2">
            <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
          </div>
      
        <!-- Testimonial Content -->
          <p class="mt-24 text-[22px] font-normal">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
          </p>
      
        <!-- Author -->
          <div class="mt-[50px]">
            <p class="font-normal text-[25px]">Mr. John</p>
            <p class="text-[17px]">Barry Maher & Associates</p>
          </div>
      
        <!-- Quote Icon Bottom Right -->
          <div class="w-[65px] h-[65px] absolute -bottom-3 right-3 bg-pink-500 text-white rounded-full">
            <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto transform scale-x-[-1] scale-y-[-1] mt-[50px] ml-[23px]">
          </div>
        </div>
      `},
      { content: `
          <div class="relative font-[Inter] mt-[100px] max-w-3xl mx-auto p-6 bg-[#0c052b] text-white rounded-[2vw] text-center shadow-lg">
          <!-- Quote Icon Top Left -->
            <div class="w-[65px] h-[65px] absolute -top-8 left-3 bg-pink-500 text-white rounded-full">
              <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto mt-[-7px] ml-[10px]">
            </div>
        
          <!-- Profile Image -->
            <div class="absolute w-[200px] h-[200px] -top-[100px] left-1/2 transform -translate-x-1/2">
              <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
            </div>
        
          <!-- Testimonial Content -->
            <p class="mt-24 text-[22px] font-normal">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
            </p>
        
          <!-- Author -->
            <div class="mt-[50px]">
              <p class="font-normal text-[25px]">Mr. John</p>
              <p class="text-[17px]">Barry Maher & Associates</p>
            </div>
        
          <!-- Quote Icon Bottom Right -->
            <div class="w-[65px] h-[65px] absolute -bottom-3 right-3 bg-pink-500 text-white rounded-full">
              <img src ="assets/quotes.png" alt="Quote" class="w-auto h-auto transform scale-x-[-1] scale-y-[-1] mt-[50px] ml-[23px]">
            </div>
          </div>
      `},
    ];
    buttonList = [
      { btnText: "PR Professionals", btnColor: "customGradient", link: "/pr-professionals" },
      { btnText: "Digital Marketers", btnColor: "gradient", link: "/marketers" },
      { btnText: "Journalist", btnColor: "customGradient", link: "/journalist" },
      { btnText: "Freelancers", btnColor: "customGradient", link: "/freelancers" }
    ];
  
    ngAfterViewInit() {
      // Check if elements are available before accessing nativeElement
      if (this.heroText) {
        gsap.from(this.heroText.nativeElement, {
          opacity: 0,
          y: 50,
          duration: 1.5,
          ease: 'power2.out',
        });
      }
  
      if (this.parallaxImage) {
        gsap.to(this.parallaxImage.nativeElement, {
          y: '-50%',
          ease: 'none',
          scrollTrigger: {
            trigger: this.parallaxImage.nativeElement,
            start: 'top bottom',
            end: 'bottom top',
            scrub: 1,
          },
        });
      }
  
      if (this.parallaxText) {
        gsap.fromTo(
          this.parallaxText.nativeElement,
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            scrollTrigger: {
              trigger: this.parallaxText.nativeElement,
              start: 'top 70%',
              end: 'center center',
              scrub: true,
            },
          }
        );
      }
  
      if (this.features) {
        gsap.utils
          .toArray(this.features.nativeElement.children)
          .forEach((card: any) => {
            gsap.fromTo(
              card,
              { scale: 0.8, opacity: 0.5 },
              {
                scale: 1.1,
                opacity: 1,
                scrollTrigger: {
                  trigger: card,
                  start: 'top 75%',
                  end: 'center center',
                  scrub: true,
                },
              }
            );
          });
      }
    }

}
