import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-link',
  templateUrl: './link.component.html',
  styleUrls: ['./link.component.css']
})
export class LinkComponent implements OnInit {
  
  @Input() linkText: string = 'Click Me';
  @Input() linkColor: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'default' = 'default';
  @Input() linkGhost: boolean = false;
  @Input() linkUnderline: boolean = false;
  @Input() linkUnderlineHover: boolean = false;
  @Input() href: string = '/';

  linkClass: string = '';

  ngOnInit() {
    this.linkClass = `link ${this.linkColor ? `link-${this.linkColor}` : 'link-default'}`;

    if (this.linkGhost) {
      this.linkClass += ` link-ghost-${this.linkColor || 'default'}`;
    }

    if (this.linkUnderline) {
      this.linkClass += ' link-underline';
    }

    if (this.linkUnderlineHover) {
      this.linkClass += ' link-underline-hover';
    }
  }
}






