<div class="min-h-screen bg-gray-50">
  <div class="flex items-center gap-2 p-4 md:p-6">
    <button class="text-gray-500 hover:text-gray-700">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="icon icon-tabler icons-tabler-outline icon-tabler-chevron-left"
      >
        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
        <path d="M15 6l-6 6l6 6" />
      </svg>
    </button>
    <h1 class="text-xl md:text-2xl font-semibold">Settings</h1>
    <button class="text-gray-500 hover:text-gray-700">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    </button>
  </div>
  <!-- Main Content Area with Sidebar -->
  <div class="flex flex-col md:flex-row">
    <!-- Left Sidebar -->
    <div class="w-full md:w-64 lg:w-72 bg-white p-4 md:p-6">
      <nav>
        <div *ngFor="let item of menuItems">
          <button
            (click)="setActiveMenu(item)"
            [class.bg-[#ECECFF]]="item.active"
            [class.text-[#0B013D]]="item.active"
            class="w-full flex items-center gap-3 px-4 py-3 rounded-lg text-gray-600 hover:bg-[#ECECFF] transition-colors"
          >
            {{ item.label }}
          </button>
        </div>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="flex-1 p-4 md:p-6">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
