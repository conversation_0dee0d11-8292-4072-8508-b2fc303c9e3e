import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { MediaDataComponent } from './pages/media-data/media-data.component';
import { MyPitchesComponent } from './pages/my-pitches/my-pitches.component';

import { SharedModule } from 'src/app/shared/shared.module';
import { AddEventComponent } from './pages/events/add-event/add-event.component';
import { AddPostComponent } from './pages/jobs/add-post/add-post.component';
import { DropdownModule } from 'primeng/dropdown';


@NgModule({
  declarations: [
    DashboardComponent,
    MediaDataComponent,
    MyPitchesComponent,
    AddEventComponent,
    AddPostComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    DashboardRoutingModule,
    SharedModule,
    DropdownModule,
  ],
})
export class DashboardModule {}
