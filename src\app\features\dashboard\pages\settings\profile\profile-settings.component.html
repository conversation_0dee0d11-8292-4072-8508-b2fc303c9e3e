<div class="max-w-full">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold">Profile Settings</h2>
   
     <div class="flex justify-end">
      <button (click)="saveProfile()"
        class="px-6 py-2.5 bg-gradient-primary text-white rounded-lg hover:opacity-90 transition-opacity">
        Save Changes
      </button>
    </div>
    <button class="text-sm px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
      Edit Profile
    </button>
  </div>

  <div>

    <!-- Profile Form -->
    <div class="space-y-6 border p-4 rounded-lg">
      <!-- Name -->
      <div>
        <label class="block text-sm font-medium text-[#11181C] mb-1">Name</label>
        <input type="text" [(ngModel)]="user.name"
          class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent">
          <label class="block text-sm font-medium text-[#999999] mb-1">This will appear on your public profile. It can be your full name or a professional display name.</label>
      
        </div>

      <!-- Email -->
      <div>
        <label class="block text-sm font-medium text-[#11181C] mb-1">Email</label>
        <input type="email" [(ngModel)]="user.email"
          class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent">
          <label class="block text-sm font-medium text-[#999999] mb-1">This is your verified email address used for communication and account notifications.</label>
      
        </div>

      <!-- Role -->
      <div>
        <label class="block text-sm font-medium text-[#11181C] mb-1">Role</label>
        <input type="text" [(ngModel)]="user.role"
          class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent">
          <label class="block text-sm font-medium text-[#999999] mb-1">Let others know your position — e.g., Journalist, PR Manager, Founder. This helps match you with relevant opportunities.</label>
      
        </div>

      <!-- Industry -->
      <div>
        <label class="block text-sm font-medium [#11181C] mb-1">Industry</label>
        <input type="text" [(ngModel)]="user.industry"
          class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent">
          <label class="block text-sm font-medium text-[#999999] mb-1">Choose the primary industry you work in. This helps tailor the platform experience to your interests.</label>
      
        </div>

      <!-- Industry Tags -->
      <div>
        <label class="block text-sm font-medium [#11181C] mb-2">Industry Tags</label>
        <div class="flex flex-wrap gap-2">
          <span *ngFor="let tag of user.industryTags"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
            {{ tag }}
            <button class="ml-1 text-gray-500 hover:text-[#777777]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </span>
          <button
            class="inline-flex items-center px-3 py-1 rounded-full text-sm border border-gray-300 text-gray-600 hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Tag
          </button>
        </div>
      </div>

      <!-- Bio -->
      <div>
        <label class="block text-sm font-medium text-[#11181C] mb-1">Bio</label>
        <textarea [(ngModel)]="user.bio" rows="4"
          class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"></textarea>
          <label class="block text-sm font-medium text-[#999999] mb-1">Write a short introduction about yourself. You can @mention organizations or link to past work.</label>

        </div>

     
    </div>
  </div>

  <div>
    <!-- Profile Card -->
    <div class="bg-gradient-primary rounded-2xl p-6 text-white mb-8">
      <div class="flex items-start gap-4">
        <div class="relative">
          <img [src]="user.avatar" alt="Profile" class="w-20 h-20 rounded-xl object-cover">
          <button (click)="uploadImage()"
            class="absolute -bottom-2 -right-2 p-1.5 bg-white rounded-lg text-gray-600 hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
        <div>
          <h3 class="text-2xl font-semibold mb-1">{{ user.name }}</h3>
          <p>{{ user.role }}</p>
        </div>
      </div>
    </div>

    <!-- Profile Form -->
    <div class="space-y-6 border rounded-lg p-6">
      <!-- Name -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-1">Name</label>
        <label class="block text-base font-semibold mb-1">John Doe</label>
      </div>

      <!-- Email -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-1">Email</label>
        <label class="block text-base font-semibold mb-1"><EMAIL></label>
      </div>

      <!-- Role -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-1">Role</label>
        <label class="block text-base font-semibold mb-1">Journalist</label>
      </div>

      <!-- Industry -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-1">Indusrty</label>
        <label class="block text-base font-semibold mb-1">IT</label>
      </div>

      <!-- Industry Tags -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-2">Industry Tags</label>
        <div class="flex flex-wrap gap-2">
          <span *ngFor="let tag of user.industryTags"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
            {{ tag }}
            <button class="ml-1 text-gray-500 hover:text-[#777777]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </span>
          <button
            class="inline-flex items-center px-3 py-1 rounded-full text-sm border border-gray-300 text-gray-600 hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Tag
          </button>
        </div>
      </div>

      <!-- Bio -->
      <div>
        <label class="block text-sm font-medium text-[#777777] mb-1">Bio</label>
        <label class="block text-base font-semibold mb-1">Opening remarks and welcome to the conference</label>
      </div>

    </div>
  </div>
</div>