<div class="min-h-screen bg-gray-50">
  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center min-h-screen">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF4D8D]"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="flex flex-col items-center justify-center min-h-screen p-4">
    <div class="text-red-600 text-xl mb-4">Failed to load PR Release details</div>
    <button (click)="goBack()" class="px-6 py-2 bg-[#FF4D8D] text-white rounded-lg hover:bg-[#FF3D7D] transition-colors">
      Go Back
    </button>
  </div>

  <!-- Content -->
  <div *ngIf="!loading && !error && release" class="max-w-7xl mx-auto px-4 py-8">
    <!-- Back Button -->
    <button (click)="goBack()" class="mb-6 flex items-center text-gray-600 hover:text-gray-900 transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
      Back to PR Releases
    </button>

    <!-- Main Content -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
      <!-- Header Section -->
      <div class="border-b border-gray-200 p-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <!-- Company Info -->
          <div class="flex items-center gap-4">
            <img [src]="release.company.logo" [alt]="release.company.name" class="w-16 h-16 rounded-lg object-cover">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">{{ release.company.name }}</h1>
              <p class="text-gray-500">{{ release.company.industry }}</p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <button (click)="applyToRelease()" 
              class="px-6 py-2.5 bg-gradient-primary text-white rounded-lg  transition-colors flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              Apply Now
            </button>
            <button (click)="saveRelease()" 
              class="p-2.5 text-gray-400 hover:text-gray-600 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
            </button>
            <button (click)="shareRelease()"
              class="p-2.5 text-gray-400 hover:text-gray-600 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Release Content -->
      <div class="p-6">
        <!-- Title and Type -->
        <div class="mb-6">
          <div class="flex items-center gap-2 mb-2">
            <span class="text-sm text-gray-500">{{ release.type }}</span>
            <span *ngIf="release.isNew" class="bg-gradient-primary text-white text-xs px-2 py-1 rounded">NEW</span>
          </div>
          <h2 class="text-3xl max-md:text-xl font-semibold text-gray-900 mb-4">{{ release.title }}</h2>
        </div>

        <!-- Tags -->
        <div class="flex flex-wrap gap-2 mb-6">
          <span *ngFor="let tag of release.tags" 
            class="bg-gray-100 text-gray-600 text-sm px-4 py-1 rounded-full">
            {{ tag }}
          </span>
        </div>

        <!-- Date Information -->
        <div class="flex flex-wrap gap-6 mb-8 text-sm text-gray-500">
          <span class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Posted: {{ release.postedAt }}
          </span>
          <span class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Deadline: {{ release.deadline }}
          </span>
        </div>

        <!-- Full Content -->
        <div class="">
          <p class="whitespace-pre-line text-lg">{{ release.fullContent }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
