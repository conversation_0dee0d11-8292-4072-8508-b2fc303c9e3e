/* Add custom styles here */

/* Table Styles */
.table-container {
  border-radius: 8px;
  overflow: hidden;
}

/* Table Wrapper for Mobile Scroll */
.table-wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #cbd5e0 #f3f4f6; /* Firefox */
}

/* Customize scrollbar for Webkit browsers */
.table-wrapper::-webkit-scrollbar {
  height: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

th {
  background-color: #f9fafb;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  white-space: nowrap;
}

td,
th {
  padding: 1rem;
  vertical-align: top;
  border-bottom: 1px solid #e5e7eb;
}

tr:last-child td {
  border-bottom: none;
}

/* Avatar and Name */
.avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  object-fit: cover;
}

/* Tags/Covers */
.tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  color: #4b5563;
  margin: 0.125rem;
}

/* Buttons */
.btn-primary {
  background-color: #ff4d8d;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #ff3d7d;
}

.btn-secondary {
  background-color: white;
  color: #4b5563;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  border: 1px solid #e5e7eb;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

/* Search Input */
.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #ff4d8d;
  box-shadow: 0 0 0 2px rgba(255, 77, 141, 0.1);
}

/* Icons */
.icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
  transition: color 0.2s;
}

.icon:hover {
  color: #374151;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 0.5rem;
}

.social-link {
  color: #9ca3af;
  transition: color 0.2s;
}

.social-link:hover {
  color: #6b7280;
}

/* Info Section */
.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

/* Filter Pills */
.filter-pills {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  /* overflow-x: auto; */
  -ms-overflow-style: none; /* Hide scrollbar IE and Edge */
  scrollbar-width: none; /* Hide scrollbar Firefox */
  background-color: #ededed;
  padding: 4px;
  border-radius: 12px;
}

/* Hide scrollbar for filter pills */
.filter-pills::-webkit-scrollbar {
  display: none;
}

.filter-pill {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.filter-pill.active {
  background-color: #ff4d8d;
  color: white;
}

.filter-pill:not(.active) {
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #4b5563;
}

.filter-pill:not(.active):hover {
  background-color: #f9fafb;
}

/* Loading State */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  td,
  th {
    min-width: 120px;
  }

  .info-column {
    min-width: 200px;
  }

  /* Keep header and filters full width */
  .header-content,
  .filter-section {
    width: 100%;
    padding-right: 0;
  }
}
