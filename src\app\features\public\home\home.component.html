<div class="relative">
  <main class="relative">
    <!-- Hero div -->
    <div class="relative">
      <div
        class="w-full font-normal flex justify-center items-center font-[Inter] bg-gradient-to-r from-[#0B013D] via-[#342c52] to-[#0B013D]"
      >
        <div
          class="w-full max-w-[62rem] px-4 pt-[100px] max-md:pt-[60px] pb-[80px] max-md:pb-[60px] flex flex-col items-center text-white text-center gap-[30px] max-md:gap-[20px]"
        >
          <h1
            class="text-[4rem] max-md:text-[2rem] max-sm:text-[1.75rem] leading-[1.2] max-md:leading-[1.3]"
          >
            A source for every brand story<br />
            Get Connected <span class="text-[#f5327d]">Get Going!</span>
          </h1>
          <p
            class="text-[24px] max-md:text-[16px] max-sm:text-[14px] leading-[1.6] max-w-[90%]"
          >
            Be part of a communication network connecting brands, experts, and
            businesses. We help journalists, PR professionals, freelancers and
            digital marketers share their narratives with the right source
          </p>
          <!-- <a href="#" class="hero__cta">Sign up Free</a> -->
        </div>
      </div>
      <svg
        width="81"
        height="128"
        viewBox="0 0 81 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        style="position: absolute; top: 300px; left: 66px"
      >
        <g opacity="0.501961">
          <rect width="81" height="128" fill="url(#pattern0_0_1)" />
        </g>
        <mask
          id="mask0_0_1"
          style="mask-type: alpha"
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="81"
          height="128"
        >
          <g opacity="0.501961">
            <rect width="81" height="128" fill="url(#pattern1_0_1)" />
          </g>
        </mask>
        <g mask="url(#mask0_0_1)">
          <rect width="81" height="128" fill="white" />
        </g>
        <defs>
          <pattern
            id="pattern0_0_1"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <use
              xlink:href="#image0_0_1"
              transform="scale(0.0123457 0.0078125)"
            />
          </pattern>
          <pattern
            id="pattern1_0_1"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <use
              xlink:href="#image0_0_1"
              transform="scale(0.0123457 0.0078125)"
            />
          </pattern>
          <image
            id="image0_0_1"
            width="81"
            height="128"
            preserveAspectRatio="none"
            xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFEAAACACAMAAABEM3WOAAABL1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC7wylAAAAZXRSTlMA//4CB9EEUSf6FgH2mgjx17jm06tGiGSeM/gQ/FtetVSgaB7pEq/tKnd9qPTdSDILcTiOQsYOlNt0gCIKJLvJom4Ucho6hZhLvTCkWmzLO8AYi5GXNkRhToLD4IR5QORY1c4gv6SWOM4AAAL6SURBVHicrdnXVlpREIDhPVsORXovUgQMiIAIiIDSFIK99xYT9f2fIUezEleu+eeOm28tzi4zs0dxsRGvP63OUZrlOpRsuTeb1TOEdJ769X5q4VMefJ/ds952XG7731/L3ZnBxY7rcn5m5Sv6+9o9AT0VrRhx0rO6pWkhwZ4/8ZP0lHXgWkdB1dZ3LLgnKyxoeTyxsmJO4I+oDAMGt+QZFlMC3DH/xZN8Xl5g7AoMKjcuRqQHi2NZhsWwpGHxm+zCojJ8tHgv/9IfFAf0ZWb+7SAtbuMne8GVhUUVS3yDxZrUYVENf9FiGl+bntcNi8pdRKseM1ZlFRYt3ktYVAV8tUt6DRb35AAWaxKGxb7YYDEuN7Bow3PshQtrLP/EXNXPguafZrsP1av64FJ8RO/GiERQzxKTQ3ShDwxpkmDenVwCz5/z9NDhaO9QnCfcrEqlvcVoE1vkPSF6P7oBYPnFiH9JRPtiR0Bp0s+VTcwRHHVviK7orWSI+GLRGpSZ4wXtKqSxZVWTsviiZLK7KwaO0HO2rcvELvmKnGyinjpNTlnQ4ss4WfHW8caC6qwAg9bENixOJAWLO1KCRbVEFwvqStPd81biAa6RVF2u4C2uLiTrgcnjhNGASXtGl69hsxFyGMdwWz5f8jkyR/AHjV8WvVfw7vSkh/qQHG98RDhYpHsh673G36BSGk5lZusr8BxBzZ280OfSJlFYtFbofKaG+APh8BUGN1xlWLyQRRYcywmaexoDeYS6GDM8jZVXkRBTj8+Hu6OgQ+SlXZsdc9bqoYCIuFq7t/3ZOYsta7ZF3sF00U4sh3N95BX9/hyH1nah25FkJo3lwPOIVzpjLqusbVbkbA/cyLaAtH5wnNoJSSBFVmSNgGTRvi3q8LJXy71U2bfEqQS5xvwjjmnwRhfzKLgQSMIj+Bg9qVvWBvzWWRby5JlxrukmcIzPbVoB8Pb6CE+Snk3ahX5ByOMvCGuSg0Wn4DPeTOUcFu3ip0eTU3lg7zJz2vlALs5vKyNJwggQhN4AAAAASUVORK5CYII="
          />
        </defs>
      </svg>
      <svg
        width="61"
        height="111"
        viewBox="0 0 61 111"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        style="position: absolute; top: 254px; right: 61px"
      >
        <mask
          id="mask0_2003_41"
          style="mask-type: alpha"
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="61"
          height="111"
        >
          <g opacity="0.501961">
            <rect width="61" height="111" fill="url(#pattern0_2003_41)" />
          </g>
        </mask>
        <g mask="url(#mask0_2003_41)">
          <rect width="61" height="111" fill="white" />
        </g>
        <defs>
          <pattern
            id="pattern0_2003_41"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <use
              xlink:href="#image0_2003_41"
              transform="scale(0.0163934 0.00900901)"
            />
          </pattern>
          <image
            id="image0_2003_41"
            width="61"
            height="111"
            preserveAspectRatio="none"
            xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAABvCAMAAABo+jgnAAABFFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABIEvRKAAAAXHRSTlMA/wL6/cU9Z+A6Cvf18UybrQ3qFtgDLgipvfzaKE/TB20RXEHBz5iNsQRXHmpgi3We4rBUGZGI3M535yDJf0WFLSSjjsemN3xj7RsqEoE0y3m05XG3WqEmXVGTSbOsZswAAAKLSURBVHicpZjncuJAEIRpgyWCAJFzzhkMJh1gTDxs8DmH8/u/x4EvlI+yRKGeP6qS9G2vtNs7s6v7PxInezeOiYQkEnQSJoKW+0TPfZKdkLbBRtB2i1k77HGeEdICKgSdM7gIunNKwGPUCHqJF4Luugm4hCpBxymHBJyEQ9bBOiH9ighBy4xDzFKekI7gO0HXGYd4nDNCWkCPoKe4JuhOiIC9lEOGlEO6jENKYBwSpxwSYByyDjIOeaUcIjMO8UkHcoj5UmU8bUoOcS2KtfOzzApqX2ZHeL833uZyMrBiFxZ3IIo3RfjE+SmHeMLFizujYUdF5bZo8663N/1oKdIC2h9XhxC3DyxbrpPPianSp09tGJU7noOpcCPWo1tBaZYtmxz7L1zBr0yHsFOU0tWmwq9t4kERdhjQmJe9HtXOKT8cf1N+9hEzfezAGyoR0w+0wzoTzgn6AU8ELePQj1GJE2eDkA5jTtAiygQ9gZegjVZqqX8kpJO4JegsBIJ2633aYReYmr2IC4LOYkTQbj2RH1tIE9JJDAnajhRBdyzEaJcQIKQrWBL0MzXJ760F7fA1mB11HMwJSJ5ZyQtWpuwWqAQ2pE5AThl3OoLMojQC9MaZf1orCpfHeyWR8592dhXYLlbRR/tGtN20VOqbr9oIp556U386JP1uxtD/cZfdiJHF1XGT2BFOJW9/yoP74N92ulpG1NNavJXbk4A7aNVA/4vMiqGNKvXr4egzPtBZMwwNZs8eoxKNC8w2bgzmBHRE5fYk4gRdQ5Ggq1SCnVPnJGkkCLrBGDRmYKb5C2SCjvzZv2qLHnVm/cydDzEDVtAzq5qXKksq2BB0Fu8EnQGR3WPBKCGte9/VU78ApiY5eHSGpngAAAAASUVORK5CYII="
          />
        </defs>
      </svg>
      <svg
        width="19"
        height="19"
        viewBox="0 0 19 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style="position: absolute; top: 268px; right: 366px"
      >
        <path
          opacity="0.501961"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9.5 0.999023C14.1945 0.999023 18 4.80452 18 9.49872C18 14.1933 14.1945 17.9988 9.5 17.9988C4.8056 17.9988 1 14.1933 1 9.49872C1 4.80452 4.8056 0.999023 9.5 0.999023Z"
          stroke="white"
          stroke-width="2"
          stroke-miterlimit="16"
        />
      </svg>
    </div>

    <!-- Announcement Image -->
    <div class="relative py-8 max-md:py-6">
      <div class="flex flex-col items-center gap-[30px] max-md:gap-[20px]">
        <img
          src="assets/announcement.png"
          alt="Announcement Illustration"
          class="w-[40%] max-md:w-[70%] h-auto"
        />
      </div>
    </div>

    <!-- Our Community -->
    <div class="community-div px-4 py-12 max-md:py-8">
      <p class="font-[Inter] text-[2.5rem] max-md:text-[1.75rem] max-sm:text-[1.5rem] flex justify-center mb-8 max-md:mb-6">
        Our Community
      </p>
      <div
        class="w-[100%] xl:w-[90%] mx-auto grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8 max-md:gap-0 place-items-center"
      >
        <app-card
          *ngFor="let card of cards"
          [imgSrc]="card.imgSrc"
          [heading]="card.heading"
          [description]="card.description"
          [link]="card.link"
        >
        </app-card>
      </div>
    </div>

    <!-- Brand Partners -->
    <div class="my-[60px]">
      <div
        class="flex mx-[auto] py-[95px] flex-col-reverse lg:flex-row bg-[#0c023e] justify-center items-center"
      >
        <!-- Carousel div -->
        <div class="w-full lg:w-1/2 px-4 flex justify-center items-center">
          <app-carousel
            [slides]="carouselImages"
            [dotColor]="'#ffffff'"
            [minHeight]="'500px'"
            [carouselWidth]="'100%'"
          ></app-carousel>
        </div>

        <!-- Text Content div -->
        <div
          class="w-full lg:w-[40%] px-6 flex flex-col text-white justify-center items-center font-[Inter] gap-[2rem] text-center lg:text-left"
        >
          <h1 class="text-[34px] max-md:text-[28px] max-sm:text-[24px] leading-[1.2]">
            Trusted by Top Media Outlets, Agencies, and Brands
          </h1>
          <p class="text-[16px] max-md:text-[14px] leading-[1.6] max-w-[90%]">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis
            ipsum suspendisse ultrices gravida.
          </p>
        </div>
      </div>
    </div>

    <!-- Testimonials -->
    <div class="my-[80px] max-md:my-[60px] px-4">
      <div
        class="flex flex-col justify-center items-center font-[Inter] gap-[50px] max-md:gap-[30px]"
      >
        <h1 class="text-[39px] max-md:text-[32px] max-sm:text-[28px] leading-[1.2] text-center">
          What People About Say
        </h1>
        <div class="flex flex-wrap justify-center gap-4 px-4">
          <app-button
            *ngFor="let button of buttonList"
            [btnSize]="'xl'"
            [btnText]="button.btnText"
            [btnColor]="button.btnColor"
            (click)="filterCarousel(button.btnText)"
          >
          </app-button>
        </div>

        <div class="w-1/2 flex justify-center items-center">
          <app-carousel
            [slides]="filteredCarousel"
            [dotColor]="'#F53076'"
            [dotColorActive]="'#180F47'"
            [carouselWidth]="'100%'"
            [minHeight]="'570px'"
          >
          </app-carousel>
        </div>
      </div>
    </div>

    <!-- Expert Directory -->
    <div class="flex flex-col justify-center items-center gap-[27px] px-4 py-12 max-md:py-8">
      <div
        class="flex flex-col justify-center items-center font-[Inter] gap-[27px] max-md:gap-[20px]"
      >
        <p class="text-[25px] max-md:text-[20px] text-[#F3264F]">Expert Directory</p>
        <h1 class="text-[#180F47] text-center text-[32px] max-md:text-[28px] max-sm:text-[24px] leading-[1.2]">
          Build Strong Connection for Better Stories
        </h1>
        <p class="text-[18px] max-md:text-[16px] leading-[1.6] w-full md:w-3/4 text-center px-4">
          CommsConnect empowers storytellers to expand their network of sources,
          experts, guests, and speakers. Explore our expert database and start
          building connections today.
        </p>
        <div
          class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 p-4"
        >
          <div
            *ngFor="let category of categories"
            class="p-6 max-md:p-4 flex justify-center items-center hover-gradient border border-[#F53359] rounded-[1vw] shadow-[0_0_30px_0px_#0C023E26] text-[18px] max-md:text-[16px] text-center"
          >
            {{ category }}
          </div>
        </div>
      </div>
      <app-button
        btnColor="gradient"
        btnSize="xl"
        [isDisabled]="false"
        [isRounded]="true"
        btnText="Explore Categories"
      >
      </app-button>
    </div>

    <!-- Spotlight -->
    <div class="my-[45px] max-md:my-[30px]">
      <div class="relative w-full flex text-white px-[2rem] md:px-[6.5rem] flex-col">
        <div
          class="absolute inset-0 bg-cover bg-center"
          style="background-image: url('assets/spotlight.png')"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#0C023EE6] via-[#0C023E] to-[#0F0253] opacity-90"
        ></div>
        <div class="relative flex justify-center mt-[52px] max-md:mt-[30px] mb-[69px] max-md:mb-[40px]">
          <app-button
            btnColor="spotlight"
            btnTextColor="#F3254D"
            btnSize="custom"
            [isDisabled]="false"
            [isRounded]="true"
            btnText="Claim the Spotlight"
          >
          </app-button>
        </div>
        <div class="relative font-[Inter] text-center sm:text-left px-4">
          <h2 class="text-[29px] max-md:text-[24px] leading-[1.4] mb-8">
            Congratulations to the CommsClub – the Best of the Best in PR and Comms
          </h2>
          <div class="text-[16px] max-md:text-[14px] leading-[1.6] space-y-6">
            <p>
              Introducing the CommsClub 100: Excellence in Public Relations
              Every quarter, we spotlight the industry's top PR professionals
              through our prestigious CommsClub 100 ranking.
            </p>
            <p>
              Our sophisticated algorithm evaluates key performance indicators, including:
            </p>
            <ul class="list-disc pl-5 space-y-2">
              <li>Swift response times</li>
              <li>Positive media engagement rate</li>
            </ul>
            <p>
              The CommsClub 100 features a mix of agency veterans and in-house
              communication experts who possess:
            </p>
            <ul class="list-disc pl-5 space-y-2">
              <li>Compelling narratives</li>
              <li>Exceptional storytelling abilities</li>
            </ul>
          </div>
        </div>
        <div class="relative flex justify-center my-8">
          <app-button
            btnColor="gradient"
            btnSize="lg"
            [isDisabled]="false"
            [isRounded]="true"
            btnText="Ready to discover PR's finest? Explore the CommsClub 50 now!"
          >
          </app-button>
        </div>
      </div>
    </div>

    <!-- Guide And Empower -->
    <div class="my-[60px] max-md:my-[40px]">
      <div class="text-center text-[Inter] bg-white px-4">
        <h1 class="text-[32px] max-md:text-[28px] mb-8 max-md:mb-6">Guide And Empower</h1>
        <div class="flex justify-center items-center">
          <app-carousel
            [slides]="guideImages"
            [dotColor]="'#F53076'"
            [dotColorActive]="'#180F47'"
            [carouselWidth]="'100%'"
            [minHeight]="'500px'"
          ></app-carousel>
        </div>
      </div>
    </div>

    <!-- <router-outlet></router-outlet> -->
  </main>
</div>
