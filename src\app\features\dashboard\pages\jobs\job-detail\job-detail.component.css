/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.min-h-screen {
  animation: fadeIn 0.3s ease-in-out;
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Hover Effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Prose Styles */
.prose {
  color: #374151;
  max-width: 65ch;
  margin: 0 auto;
}

.prose p {
  margin-bottom: 1.5em;
  line-height: 1.75;
}

.prose ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-bottom: 1.5em;
}

.prose li {
  margin-bottom: 0.5em;
}

/* Lists Styling */
.list-disc {
  list-style-type: disc;
  padding-left: 1.25rem;
}

.list-inside {
  list-style-position: inside;
}

/* Responsive Design */
@media (max-width: 768px) {
  .prose {
    font-size: 0.95rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 