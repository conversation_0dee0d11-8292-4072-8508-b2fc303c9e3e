<div class="settings-container">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <p class="header">Overview</p>
    <div class="tabs-nav">
      <button
        class="tab-item"
        [class.active]="activeTab === 'overview'"
        (click)="setActiveTab('overview')"
      >
        Overview
      </button>
      <button
        class="tab-item"
        [class.active]="activeTab === 'credit-activity'"
        (click)="setActiveTab('credit-activity')"
      >
        Credit Activity
      </button>
      <button
        class="tab-item"
        [class.active]="activeTab === 'invoices'"
        (click)="setActiveTab('invoices')"
      >
        Invoices
      </button>
    </div>
  </div>

  <!-- Content -->
  <div class="settings-content">
    <!-- Tabs -->

    <!-- Overview Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'overview'">
      <!-- Profile Card -->
      <div class="bg-gradient-primary rounded-2xl p-6 text-white mb-8">
        <div class="flex items-start gap-4">
          <div class="relative">
            <img
              [src]="user.avatar"
              alt="Profile"
              class="w-20 h-20 rounded-xl object-cover"
            />
            <button
              class="absolute -bottom-2 -right-2 p-1.5 bg-white rounded-lg text-gray-600 hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </button>
          </div>
          <div>
            <h3 class="text-2xl font-semibold mb-1">{{ user.name }}</h3>
            <p>{{ user.role }}</p>
          </div>
        </div>
      </div>

      <!-- Basic Info -->
      <div class="info-card">
        <div
          class="card-header bg-[#ECECFF] px-[1rem] py-[0.5rem] rounded-tl-[0.75rem] rounded-tr-[0.75rem]"
        >
          <h2 class="card-title">Basic</h2>
          <button class="edit-button" (click)="editBillingInfo()">
            Edit Billing Info
          </button>
        </div>
        <div class="info-grid px-[1rem] pb-[1rem]">
          <div class="info-item">
            <span class="info-label">Name</span>
            <span class="info-value">{{ billingInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email</span>
            <span class="info-value">{{ billingInfo.email }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Company Name</span>
            <span class="info-value">{{ billingInfo.companyName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Address</span>
            <span class="info-value">{{ billingInfo.address }}</span>
          </div>
        </div>
      </div>

      <!-- Payment Methods -->
      <div class="info-card">
        <div
          class="card-header bg-[#ECECFF] px-[1rem] py-[0.5rem] rounded-tl-[0.75rem] rounded-tr-[0.75rem]"
        >
          <h2 class="card-title">Payment Method</h2>
          <button class="edit-button" (click)="editBillingInfo()">
            Payment Method
          </button>
        </div>
        <div
          class="flex flex-row justify-between items-center px-[1rem] pb-[1rem]"
        >
          <div class="flex flex-row gap-3">
            <div class="info-item">
              <span class="info-label">Name</span>
              <span class="info-value">{{ billingInfo.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Email</span>
              <span class="info-value">{{ billingInfo.email }}</span>
            </div>
          </div>
          <div class="info-item">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Credit Activity Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'credit-activity'">
      <!-- Credit activity content here -->
      <p>Credit Activity Content</p>
    </div>

    <!-- Invoices Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'invoices'">
      <!-- Invoices content here -->
      <p>Invoices Content</p>
    </div>
  </div>
</div>

<!-- 8888888888888888888888888888888888  for company code 8888888888888888888888888888888888888 -->
