<div
	class="mx-auto font-[Inter] flex flex-col border border-gray-300 shadow-lg rounded-[30px] md:rounded-[50px] bg-white w-full max-w-[524px] min-h-[580px] md:h-[640px]">

	<!-- Radial Gradient -->
	<div class="flex justify-center items-center w-full h-[100px] md:h-[126px] rounded-t-[30px] md:rounded-t-[50px] bg-radial-custom p-4">
		<p class="text-white text-[20px] md:text-[27px] text-center">Create Your Free Media Account Here</p>
	</div>

	<form [formGroup]="contactForm">
		<div class="p-4 md:p-6">
			<div class="form-group space-y-3 md:space-y-4">
				<div class="form-field flex flex-col">
					<label class="form-label text-base md:text-lg mb-1">First Name *</label>
					<input type="text" formControlName="firstName"
						class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full" required />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-base md:text-lg mb-1">Last Name *</label>
					<input type="text" formControlName="lastName"
						class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full" />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-base md:text-lg mb-1">Email *</label>
					<input type="email" formControlName="email"
						class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full" />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-base md:text-lg mb-1">Signup Code?</label>
					<input type="password" formControlName="signup"
						class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full" />
				</div>

				<!-- Button -->
				<div class="flex justify-center mt-4 md:mt-6">
					<app-button (onBtnClick)="onSubmit()" btnText="Continue" btnColor="gradient"
						[isRounded]="true"></app-button>
				</div>
			</div>
		</div>
	</form>

</div>