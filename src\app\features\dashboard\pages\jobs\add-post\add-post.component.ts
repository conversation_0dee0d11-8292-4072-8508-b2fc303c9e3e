import { Component } from '@angular/core';

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}
@Component({
  selector: 'app-add-post',
  templateUrl: './add-post.component.html',
  styleUrls: ['./add-post.component.css'],
})
export class AddPostComponent {
  steps = ['Details', 'Description', 'Preview'];
  currentStep = 1;

  // Variable to store editor content
  editorContent: string = '';

  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png',
  };

  uploadImage(): void {
    // Implement image upload functionality
    console.log('Upload image clicked');
  }

  saveProfile(): void {
    // Implement profile save functionality
    console.log('Save profile clicked', this.user);
    console.log('Editor content:', this.editorContent);
  }

  // Method to get the current editor content
  getEditorContent(): string {
    return this.editorContent;
  }

  // Method to set editor content programmatically
  setEditorContent(content: string): void {
    this.editorContent = content;
  }
}
