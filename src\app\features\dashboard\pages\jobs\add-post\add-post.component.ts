import { Component } from '@angular/core';

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}
@Component({
  selector: 'app-add-post',
  templateUrl: './add-post.component.html',
  styleUrls: ['./add-post.component.css'],
})
export class AddPostComponent {
  steps = ['Details', 'Description', 'Preview'];
  currentStep = 1;

  // Variable to store editor content
  editorContent: string = '';

  // Quill editor configuration
  quillModules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link'],
    ],
  };

  user: User = {
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png',
  };

  uploadImage(): void {
    // Implement image upload functionality
    console.log('Upload image clicked');
  }

  saveProfile(): void {
    // Implement profile save functionality
    console.log('Save profile clicked', this.user);
    console.log('Editor content:', this.editorContent);
  }

  // Method to get the current editor content
  getEditorContent(): string {
    return this.editorContent;
  }

  // Method to set editor content programmatically
  setEditorContent(content: string): void {
    this.editorContent = content;
  }
}
