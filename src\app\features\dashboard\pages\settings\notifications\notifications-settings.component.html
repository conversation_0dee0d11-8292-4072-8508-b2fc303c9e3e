<div class="max-w-full">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h2 class="header">Notification Settings</h2>
    </div>
  </div>

  <!-- Notification Settings List -->
  <div class="space-y-6 rounded-lg border border-gray-200 py-2">
    <div
      *ngFor="let setting of notificationSettings"
      class="bg-white px-2 rounded-lg"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center justify-between gap-2">
          <div class="bg-[#F2F2F2] p-2 rounded-lg">
            <div class="icon" [innerHTML]="setting.icon"></div>
          </div>

          <p class="text-base text-lightBlack">{{ setting.title }}</p>
        </div>
        <button
          (click)="toggleNotification(setting)"
          [class.bg-[#0B013D]]="setting.enabled"
          [class.bg-gray-200]="!setting.enabled"
          class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out"
        >
          <span
            [class.translate-x-5]="setting.enabled"
            [class.translate-x-0]="!setting.enabled"
            class="pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
          >
            <span
              [class.opacity-0]="setting.enabled"
              [class.opacity-100]="!setting.enabled"
              class="absolute inset-0 flex h-full w-full items-center justify-center transition-opacity"
            >
              <svg
                class="h-3 w-3 text-gray-400"
                fill="none"
                viewBox="0 0 12 12"
              >
                <path
                  d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
            <span
              [class.opacity-100]="setting.enabled"
              [class.opacity-0]="!setting.enabled"
              class="absolute inset-0 flex h-full w-full items-center justify-center transition-opacity"
            >
              <svg
                class="h-3 w-3 text-[#0B013D]"
                fill="currentColor"
                viewBox="0 0 12 12"
              >
                <path
                  d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z"
                />
              </svg>
            </span>
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
