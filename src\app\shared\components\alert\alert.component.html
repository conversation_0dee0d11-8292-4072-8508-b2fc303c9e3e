<div
  *ngIf="visible"
  class="p-4 mb-4 rounded-md border {{ bgClass }} {{ borderClass }}"
  role="alert"
>
  <div class="flex items-center">
    <!-- Success Icon -->
    <svg
      *ngIf="type === 'success'"
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 mr-2 {{ iconClass }}"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M5 13l4 4L19 7"
      />
    </svg>

    <!-- Info Icon -->
    <svg
      *ngIf="type === 'info'"
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 mr-2 {{ iconClass }}"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>

    <!-- Warning Icon -->
    <svg
      *ngIf="type === 'warning'"
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 mr-2 {{ iconClass }}"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
      />
    </svg>

    <!-- Error Icon -->
    <svg
      *ngIf="type === 'error'"
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 mr-2 {{ iconClass }}"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>

    <div class="ml-1 {{ textClass }}">
      {{ message }}
    </div>

    <!-- Close Button -->
    <button
      *ngIf="dismissible"
      type="button"
      class="ml-auto -mx-1.5 -my-1.5 bg-transparent {{
        textClass
      }} rounded-lg p-1.5 hover:bg-gray-200 inline-flex h-8 w-8"
      (click)="dismiss()"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </button>
  </div>
</div>
