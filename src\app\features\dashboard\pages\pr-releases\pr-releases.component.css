/* PR Releases specific styles */
.filter-tab.active {
  color: #FF4D8D;
  border-bottom: 2px solid #FF4D8D;
}

.tag {
  background-color: #F3F4F6;
  color: #4B5563;
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.new-badge {
  background-color: #FF4D8D;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Checkbox Custom Styles */
input[type="checkbox"] {
  cursor: pointer;
}

input[type="checkbox"]:checked {
  background-color: #FF4D8D;
  border-color: #FF4D8D;
}

/* Card Hover Effects */
.release-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.release-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Button Hover Effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Tag Styles */
.tag {
  transition: all 0.2s ease;
}

.tag:hover {
  background-color: #E5E7EB;
}

/* Filter Section Styles */
.filter-section {
  background-color: #F8F7FF;
  border-radius: 12px;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}

.filter-section:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Search Input Focus Styles */
.search-input:focus {
  box-shadow: 0 0 0 2px rgba(255, 77, 141, 0.2);
}

/* Refresh Button Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-icon {
  transition: transform 0.3s ease;
}

.refresh-icon:hover {
  animation: spin 1s linear infinite;
}

/* Sidebar Styles */
.sticky {
  position: sticky;
  top: 80px;
  height: calc(100vh - 80px);
  overflow-y: auto;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.sticky::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.sticky {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Custom Checkbox and Radio Styles */
input[type="checkbox"], input[type="radio"] {
  cursor: pointer;
}

input[type="checkbox"]:checked, input[type="radio"]:checked {
  background-color: #FF4D8D;
  border-color: #FF4D8D;
}

/* Responsive Layout */
@media (max-width: 768px) {
  .sticky {
    position: relative;
    top: 0;
    height: auto;
    max-height: none;
  }

  .header-section {
    position: relative;
  }
}

/* Animation for filter changes */
.filter-transition {
  transition: all 0.3s ease-in-out;
}

/* Filter section animations */
.filter-section.collapsed {
  max-height: 0;
}

.filter-section.expanded {
  max-height: 500px;
}

/* Loading state animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Focus styles */
:focus {
  outline: none;
  ring: 2px;
  ring-color: #FF4D8D;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Header Styles */
.header-section {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
}

/* Tabs Scrollbar */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #FF4D8D transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: #FF4D8D;
  border-radius: 2px;
}

/* Card Styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Card Grid Layout */
.grid {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Card Animations */
.hover\:shadow-lg {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* Button Styles */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Loading States */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Focus Styles */
:focus {
  outline: none;
  ring: 2px;
  ring-color: #FF4D8D;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
} 