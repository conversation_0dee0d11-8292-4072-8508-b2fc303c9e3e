import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../api.service';
import { User } from '../../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly path = '/users';

  constructor(private apiService: ApiService) {}

  /**
   * Get all users (admin only)
   */
  getUsers(params: any = {}): Observable<User[]> {
    return this.apiService.get<User[]>(this.path, params);
  }

  /**
   * Get user by ID
   * @param id User ID
   */
  getUserById(id: string): Observable<User> {
    return this.apiService.get<User>(`${this.path}/${id}`);
  }

  /**
   * Get current user profile
   */
  getCurrentUserProfile(): Observable<User> {
    return this.apiService.get<User>(`${this.path}/me`);
  }

  /**
   * Update user
   * @param id User ID
   * @param userData Updated user data
   */
  updateUser(id: string, userData: Partial<User>): Observable<User> {
    return this.apiService.put<User>(`${this.path}/${id}`, userData);
  }

  /**
   * Update current user profile
   * @param userData Updated user data
   */
  updateProfile(userData: Partial<User>): Observable<User> {
    return this.apiService.put<User>(`${this.path}/me`, userData);
  }

  /**
   * Delete user (admin only)
   * @param id User ID
   */
  deleteUser(id: string): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
