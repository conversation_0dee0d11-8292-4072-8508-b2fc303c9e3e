<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-4">Post a Job Opportunity</h1>
  <app-stepper [steps]="steps" [currentStep]="currentStep"></app-stepper>
  <div class="flex justify-end mt-8">
    <button
      *ngIf="currentStep > 1"
      class="bg-gray-300 text-gray-700 px-6 py-2.5 rounded-lg mr-4"
      (click)="currentStep = currentStep - 1"
    >
      Previous
    </button>
    <button
      *ngIf="currentStep < steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
      (click)="currentStep = currentStep + 1"
    >
      Next
    </button>
    <button
      *ngIf="currentStep === steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
    >
      Submit
    </button>
  </div>
  <div class="mt-8">
    <div *ngIf="currentStep === 1">
      <h2 class="text-xl font-semibold mb-4">Details</h2>
      <!-- Details form -->
      <div class="space-y-6 border p-4 rounded-lg">
        <!-- Name -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Job Title</label
          >
          <input
            type="text"
            [(ngModel)]="user.name"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Publicly displayed job title. Ensure it is brief and
            informative.</label
          >
        </div>

        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Posting for (Company)</label
          >
          <input
            type="text"
            [(ngModel)]="user.email"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Enter the organization name this job is associated with.</label
          >
        </div>

        <!-- Role -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Job Location</label
          >
          <input
            type="text"
            [(ngModel)]="user.role"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Please provide the location of the company.</label
          >
        </div>

        <!-- Industry -->
        <div>
          <label class="block text-sm font-medium [#11181C] mb-1"
            >Industry</label
          >
          <input
            type="text"
            [(ngModel)]="user.industry"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Choose the primary industry you work in. This helps tailor the
            platform experience to your interests.</label
          >
        </div>

        <!-- Bio -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Bio</label
          >
          <textarea
            [(ngModel)]="user.bio"
            rows="4"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          ></textarea>
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Write a short introduction about yourself. You can @mention
            organizations or link to past work.</label
          >
        </div>
      </div>
    </div>
    <div *ngIf="currentStep === 2">
      <h2 class="text-xl font-semibold mb-4">Description</h2>
      <p>
        Provide a detailed description of the job responsibilities and
        requirements.
      </p>
    </div>
    <div *ngIf="currentStep === 3">
      <h2 class="text-xl font-semibold mb-4">Preview</h2>
      <p>Review the job posting before submitting.</p>
    </div>
  </div>
</div>
