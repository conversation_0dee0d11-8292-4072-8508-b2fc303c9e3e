import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';
import { AlertType } from '../../../shared/components/alert/alert.component';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  error: string = '';
  loading: boolean = false;

  highlights = [
    {
      title: 'Read Receipts',
      description:
        "Say goodbye to pitching into the void. Our network instantly notifies you when media professionals read your pitch or view your source's profile.",
      image: 'assets/showcase.png',
      gap: 'gap-[49px]',
      textLeading: 'leading-[29px]',
    },
    {
      title: 'Pitch Intelligence',
      description:
        "Quickly identify high-priority media requests with less competition using CommsClub's live reporter engagement and network activity data.",
      image: 'assets/expand.png',
      gap: 'gap-[35px]',
      textLeading: 'leading-[30px]',
    },
    {
      title: 'Topical Alerts',
      description:
        "Our advanced matching technology notifies you of real-time media opportunities that align with your sources' specific expertise.",
      image: 'assets/support.png',
      gap: 'gap-[40px]',
      textLeading: 'leading-[29px]',
    },
    {
      title: 'Expert Profiles',
      description:
        'Create free profiles for your thought leaders and let media professionals approach you with targeted opportunities.',
      image: 'assets/media.png',
      gap: 'gap-[28.9px]',
      textLeading: 'leading-[29px]',
    },
  ];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }

  ngOnInit(): void {
    // If already logged in, redirect to appropriate page
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.loading = true;
      this.error = '';

      this.authService.login(this.loginForm.value).subscribe({
        next: (user) => {
          // Check if user has admin role
          if (user.roles?.includes('ADMIN')) {
            this.router.navigate(['/admin']);
          } else {
            this.router.navigate(['/dashboard']);
          }
        },
        error: (error) => {
          this.error = error.message || 'Login failed. Please try again.';
          this.loading = false;
        }
      });
    }
  }
}
