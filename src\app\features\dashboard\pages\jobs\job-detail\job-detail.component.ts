import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';

interface Company {
  name: string;
  logo: string;
  industry: string;
}

interface Job {
  id: string;
  company: Company;
  type: string;
  title: string;
  description: string;
  tags: string[];
  postedAt: string;
  deadline: string;
  isNew: boolean;
  location: string;
  salary: string;
  experience: string;
  fullContent?: string;
  requirements?: string[];
  responsibilities?: string[];
  benefits?: string[];
}

@Component({
  selector: 'app-job-detail',
  templateUrl: './job-detail.component.html',
  styleUrls: ['./job-detail.component.css']
})
export class JobDetailComponent implements OnInit {
  job: Job | undefined;
  loading = true;
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id');
    this.fetchJobDetails(id);
  }

  private fetchJobDetails(id: string | null) {
    if (!id) {
      this.error = true;
      this.loading = false;
      return;
    }

    // Simulating API call
    setTimeout(() => {
      this.job = {
        id: id,
        company: {
          name: 'TechPulse',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Technology'
        },
        type: 'FULL TIME',
        title: 'Senior Frontend Developer',
        description: 'We are looking for an experienced Frontend Developer to join our dynamic team...',
        tags: ['Angular', 'TypeScript', 'Frontend'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
        location: 'Remote',
        salary: '$120k - $150k',
        experience: '5+ years',
        fullContent: 'We are seeking a talented and experienced Senior Frontend Developer to join our growing team at TechPulse. As a Senior Frontend Developer, you will play a crucial role in building and maintaining our cutting-edge web applications.\n\nThe ideal candidate will have a strong background in Angular and modern web technologies, with a passion for creating exceptional user experiences.',
        requirements: [
          'Bachelor\'s degree in Computer Science or related field',
          '5+ years of experience in frontend development',
          'Expert knowledge of Angular, TypeScript, and RxJS',
          'Strong understanding of responsive design and cross-browser compatibility',
          'Experience with state management (NgRx, Redux)',
          'Excellent problem-solving and debugging skills',
          'Strong communication and teamwork abilities'
        ],
        responsibilities: [
          'Lead frontend development initiatives and mentor junior developers',
          'Design and implement new features using Angular and modern web technologies',
          'Optimize applications for maximum speed and scalability',
          'Collaborate with backend developers and designers',
          'Write clean, maintainable, and well-documented code',
          'Participate in code reviews and technical discussions',
          'Stay up-to-date with emerging technologies and industry trends'
        ],
        benefits: [
          'Competitive salary package',
          'Remote work flexibility',
          'Health, dental, and vision insurance',
          '401(k) matching',
          'Professional development budget',
          'Flexible vacation policy',
          'Modern equipment and tools'
        ]
      };
      this.loading = false;
    }, 1000);
  }

  goBack() {
    this.location.back();
  }

  applyToJob() {
    // Implement apply logic
    console.log('Applying to job:', this.job?.id);
  }

  shareJob() {
    // Implement share logic
    console.log('Sharing job:', this.job?.id);
  }

  saveJob() {
    // Implement save logic
    console.log('Saving job:', this.job?.id);
  }
} 