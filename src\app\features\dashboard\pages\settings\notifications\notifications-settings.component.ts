import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON>ti<PERSON>, SafeHtml } from '@angular/platform-browser';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  icon: SafeHtml;
}

@Component({
  selector: 'app-notifications-settings',
  templateUrl: './notifications-settings.component.html',
  styleUrls: ['./notifications-settings.component.css'],
})
export class NotificationsSettingsComponent {
  notificationSettings: NotificationSetting[];

  constructor(private sanitizer: DomSanitizer) {
    const svgIcon = `
     <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-refresh"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4" /><path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4" /></svg>
    `;

    this.notificationSettings = [
      {
        id: 'email',
        title: 'New jobs matching interest',
        description:
          'Receive notifications via email for important updates and activities.',
        enabled: true,
        icon: this.sanitizer.bypassSecurityTrustHtml(
          `<svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-refresh"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4" /><path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4" /></svg>`
        ),
      },
      {
        id: 'push',
        title: 'New PR releases in your beat',
        description:
          'Get instant notifications on your desktop for real-time updates.',
        enabled: true,
        icon: this.sanitizer.bypassSecurityTrustHtml(
          `<svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-mail-share"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M13 19h-8a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v6" /><path d="M3 7l9 6l9 -6" /><path d="M16 22l5 -5" /><path d="M21 21.5v-4.5h-4.5" /></svg>`
        ),
      },
      {
        id: 'marketing',
        title: 'Weekly summaries',
        description:
          'Receive updates about new features, promotions, and news.',
        enabled: false,
        icon: this.sanitizer.bypassSecurityTrustHtml(
          `<svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-notification"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3" /><path d="M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" /></svg>`
        ),
      },
    ];
  }

  toggleNotification(setting: NotificationSetting): void {
    setting.enabled = !setting.enabled;
    console.log('Notification setting updated:', setting);
  }

  saveSettings(): void {
    console.log('Saving notification settings:', this.notificationSettings);
  }
}
