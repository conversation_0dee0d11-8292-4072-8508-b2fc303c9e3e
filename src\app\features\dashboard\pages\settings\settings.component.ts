import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  active: boolean;
  route: string;
}

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  bio: string;
  avatar: string;
  industryTags: string[];
}

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css']
})
export class SettingsComponent implements OnInit {
  menuItems: MenuItem[] = [
    {
      id: 'profile',
      label: 'Profile',
      icon: 'user',
      active: true,
      route: 'profile'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'bell',
      active: false,
      route: 'notifications'
    },
    {
      id: 'billing',
      label: 'Billing & Credits',
      icon: 'credit-card',
      active: false,
      route: 'billing'
    },
    {
      id: 'security',
      label: 'Account & Security',
      icon: 'shield-check',
      active: false,
      route: 'security'
    },
    {
      id: 'company',
      label: 'Company',
      icon: 'building',
      active: false,
      route: 'company'
    }
  ];

  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Journalist',
    industry: 'IT',
    bio: 'Opening remarks and welcome to the conference',
    avatar: 'assets/images/avatar.png',
    industryTags: ['PRODUCTIVITY', 'TECHNEWS']
  };

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  setActiveMenu(selectedItem: MenuItem): void {
    this.menuItems.forEach(item => {
      item.active = item.id === selectedItem.id;
    });
    this.router.navigate(['dashboard', 'settings', selectedItem.route]);
  }

  uploadImage(): void {
    // Implement image upload functionality
    console.log('Upload image');
  }

  saveProfile(): void {
    // Implement save profile functionality
    console.log('Save profile', this.user);
  }
}
