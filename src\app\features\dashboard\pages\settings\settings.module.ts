import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { SettingsComponent } from './settings.component';
import { ProfileSettingsComponent } from './profile/profile-settings.component';
import { NotificationsSettingsComponent } from './notifications/notifications-settings.component';
import { BillingSettingsComponent } from './billing/billing-settings.component';
import { SecuritySettingsComponent } from './security/security-settings.component';
import { CompanyComponent } from './company/company.component';

@NgModule({
  declarations: [
    SettingsComponent,
    ProfileSettingsComponent,
    NotificationsSettingsComponent,
    BillingSettingsComponent,
    SecuritySettingsComponent,
    CompanyComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule
  ]
})
export class SettingsModule { } 