<app-stepper [steps]="steps" [currentStep]="currentStep"></app-stepper>

<div class="p-6">
  <div *ngIf="currentStep === 1">
    <h2 class="text-xl font-semibold mb-4">Event Info</h2>
    <!-- Form for Event Info -->
  </div>
  <div *ngIf="currentStep === 2">
    <h2 class="text-xl font-semibold mb-4">Event Details</h2>
    <!-- Form for Event Details -->
  </div>
  <div *ngIf="currentStep === 3">
    <h2 class="text-xl font-semibold mb-4">Additional Info</h2>
    <!-- Form for Additional Info -->
  </div>
  <div *ngIf="currentStep === 4">
    <h2 class="text-xl font-semibold mb-4">Review</h2>
    <!-- Review content -->
  </div>
</div>

<div class="flex justify-end p-6">
  <button
    (click)="prevStep()"
    [disabled]="currentStep === 1"
    class="bg-gray-300 text-gray-700 px-6 py-2.5 rounded-lg mr-4"
  >
    Back
  </button>
  <button
    (click)="nextStep()"
    [disabled]="currentStep === steps.length"
    class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
  >
    Next
  </button>
</div>
