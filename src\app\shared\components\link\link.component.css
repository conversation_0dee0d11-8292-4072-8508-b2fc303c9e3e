.link-default {
    color: white !important;
}

.link {
  text-decoration: none;
  font-size: 17px;
  padding: 5px 10px;
  transition: all 0.3s ease-in-out;
}

/* Predefined Colors */
.link-primary {
  color: blue;
}

.link-secondary {
  color: purple;
}
.link-success {
  color: green;
}
.link-error {
  color: red;
}
.link-warning {
  color: yellow;
}

/* Ghost Styles */
.link-ghost-primary {
  background: transparent;
}
.link-ghost-error {
  background: transparent;
  border: 1px solid red;
}

/* Underline */
.link-underline {
  text-decoration: underline;
}

/* Underline on Hover */
.link-underline-hover:hover {
  text-decoration: underline;
}

