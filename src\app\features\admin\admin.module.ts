import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminRoutingModule } from './admin-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';
import { PrReleasesComponent } from './components/pr-releases/pr-releases.component';
import { JobsComponent } from './components/jobs/jobs.component';
import { EventsComponent } from './components/events/events.component';
import { MediaDatabaseComponent } from './components/media-database/media-database.component';
import { MyPitchesComponent } from './components/my-pitches/my-pitches.component';

@NgModule({
  declarations: [
    AdminDashboardComponent,
    PrReleasesComponent,
    JobsComponent,
    EventsComponent,
    MediaDatabaseComponent,
    MyPitchesComponent
  ],
  imports: [
    CommonModule,
    AdminRoutingModule,
    SharedModule
  ]
})
export class AdminModule { }
