<!-- Full screen loader with overlay -->
<div
  *ngIf="fullScreen"
  class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50"
>
  <div class="flex flex-col items-center">
    <svg
      class="animate-spin {{ sizeClass }} {{ colorClass }}"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
    <span *ngIf="message" class="mt-2 text-white">{{ message }}</span>
  </div>
</div>

<!-- Regular loader -->
<div
  *ngIf="!fullScreen && overlay"
  class="relative w-full h-full min-h-24 flex items-center justify-center bg-gray-100 bg-opacity-50"
>
  <div class="flex flex-col items-center">
    <svg
      class="animate-spin {{ sizeClass }} {{ colorClass }}"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
    <span *ngIf="message" class="mt-2">{{ message }}</span>
  </div>
</div>

<!-- Inline loader -->
<div *ngIf="!fullScreen && !overlay" class="inline-flex items-center">
  <svg
    class="animate-spin {{ sizeClass }} {{ colorClass }}"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      class="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      stroke-width="4"
    ></circle>
    <path
      class="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
  <span *ngIf="message" class="ml-2">{{ message }}</span>
</div>
