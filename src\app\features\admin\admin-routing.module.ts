import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';
import { PrReleasesComponent } from './components/pr-releases/pr-releases.component';
import { JobsComponent } from './components/jobs/jobs.component';
import { EventsComponent } from './components/events/events.component';
import { MediaDatabaseComponent } from './components/media-database/media-database.component';
import { MyPitchesComponent } from './components/my-pitches/my-pitches.component';

const routes: Routes = [
  {
    path: '',
    component: AdminDashboardComponent,
    children: [
      { path: '', redirectTo: 'pr-releases', pathMatch: 'full' },
      { path: 'pr-releases', component: PrReleasesComponent },
      { path: 'jobs', component: JobsComponent },
      { path: 'events', component: EventsComponent },
      { path: 'media-database', component: MediaDatabaseComponent },
      { path: 'my-pitches', component: MyPitchesComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule { }
