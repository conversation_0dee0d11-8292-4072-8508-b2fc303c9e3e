import { Component, OnInit, Input, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit {
  @Input() isPublic: boolean = false;
  isDropdownOpen: boolean = false;
  isMobileMenuOpen: boolean = false;
  userCredits: number = 500;
  userImage: string = 'assets/images/default-avatar.png';
  userName = '<PERSON>';
  userEmail = '<EMAIL>';

  navItems = [
    { path: '/admin/pr-releases', label: 'PR Releases' },
    { path: '/admin/jobs', label: 'Jobs' },
    { path: '/admin/events', label: 'Events' },
    { path: '/admin/media-database', label: 'Media Database' },
    { path: '/admin/my-pitches', label: 'My Pitches' }
  ];

  publicNavItems = [
    { path: '/', label: 'Home' },
    { path: '/solutions', label: 'Solutions' },
    { path: '/resources', label: 'Resources' },
    { path: '/expert-database', label: 'Expert Database' }
  ];

  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // You can fetch actual user data here
  }

  @HostListener('window:click', ['$event'])
  onClick(event: MouseEvent) {
    // Close dropdown when clicking outside
    const target = event.target as HTMLElement;
    if (!target.closest('.user-menu')) {
      this.isDropdownOpen = false;
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    // Close mobile menu on window resize
    if (window.innerWidth >= 1024) { // lg breakpoint
      this.isMobileMenuOpen = false;
    }
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    // Close dropdown when opening mobile menu
    if (this.isMobileMenuOpen) {
      this.isDropdownOpen = false;
    }
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }

  isActive(path: string): boolean {
    return this.router.isActive(path, true);
  }

  get displayNavItems() {
    return this.isPublic ? this.publicNavItems : this.navItems;
  }
}
