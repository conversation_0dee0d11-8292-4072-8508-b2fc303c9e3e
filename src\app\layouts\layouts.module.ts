import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PublicLayoutComponent } from './public-layout/public-layout.component';
import { AdminLayoutComponent } from './admin-layout/admin-layout.component';
import { UserLayoutComponent } from './user-layout/user-layout.component';
import { AuthLayoutComponent } from './auth-layout/auth-layout.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [
    PublicLayoutComponent,
    AdminLayoutComponent,
    UserLayoutComponent,
    AuthLayoutComponent,
  ],
  imports: [CommonModule, RouterModule, SharedModule],
  exports: [
    PublicLayoutComponent,
    AdminLayoutComponent,
    UserLayoutComponent,
    AuthLayoutComponent,
    SharedModule,
  ],
})
export class LayoutsModule {}
