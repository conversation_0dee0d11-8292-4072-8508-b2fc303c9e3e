{"name": "commsclubapp", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "engines": {"node": "20.x"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^20.2.2", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@fontsource/inter": "^5.1.1", "gsap": "^3.12.7", "jquery": "^3.7.1", "ngx-quill": "^21.0.0", "ngx-slick-carousel": "15.0.0", "primeicons": "^7.0.0", "primeng": "^20.1.1", "quill": "^1.3.7", "rippleui": "^1.12.1", "rxjs": "~7.8.0", "slick-carousel": "^1.8.1", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.7", "@angular/cli": "^16.2.7", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/quill": "^1.3.10", "autoprefixer": "^10.4.20", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.1.3"}, "resolutions": {"strip-ansi": "6.0.1", "string-width": "4.2.3", "wrap-ansi": "7.0.0"}}