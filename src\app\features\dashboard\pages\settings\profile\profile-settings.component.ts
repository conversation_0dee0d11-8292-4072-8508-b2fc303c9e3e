import { Component } from '@angular/core';

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.component.html',
  styleUrls: ['./profile-settings.component.css']
})
export class ProfileSettingsComponent {
  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png'
  };

  uploadImage(): void {
    // Implement image upload functionality
    console.log('Upload image clicked');
  }

  saveProfile(): void {
    // Implement profile save functionality
    console.log('Save profile clicked', this.user);
  }
} 