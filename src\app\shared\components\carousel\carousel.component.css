::ng-deep .slick-dots li button:before {
    font-size: 15px;
    color: var(--dot-color, white) !important;
  }
  ::ng-deep .slick-dots li:hover button:before {
    color: var(--dot-color-active, white) !important;
    opacity: 0.8; 
  }
  
  ::ng-deep .slick-dots li.slick-active button:before {
    color: var(--dot-color-active, white) !important; 
  }

::ng-deep .slick-dots {
    position: static !important;
    /* margin-top: 1.25rem; */
  }
.carousel {
    width: var(--carousel-width, 60%) !important;
    max-width: 80vw; 
    margin: auto;
    overflow: hidden; 
}

.carousel-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: var(--min-height, 500px); 
    /* min-height: 570px;  */
}