<section
  class="w-full flex flex-col lg:flex-row items-center justify-around py-6 md:py-10 bg-white min-h-[700px] relative px-4 md:px-8 gap-8"
>
  <!-- Left Column (Images + Text) -->
  <div
    class="w-full lg:w-1/2 flex flex-col items-start text-left order-1 max-md:order2"
  >
    <h3 class="px-2 md:px-6 text-[24px] md:text-[30px] leading-[1.2] mb-4">
      Amplify Your Presence with CommsClub
    </h3>
    <div class="space-y-4 md:space-y-6 p-2 md:p-6 font-[Inter]">
      <div
        *ngFor="let highlight of highlights"
        class="flex flex-col md:flex-row items-start md:items-center gap-4 md:gap-6"
      >
        <img
          [src]="highlight.image"
          [alt]="highlight.title"
          class="w-auto h-auto"
        />
        <div class="flex flex-col gap-2">
          <p class="leading-none tracking-normal text-[18px] md:text-[20px]">
            {{ highlight.title }}
          </p>
          <p class="text-base" [ngClass]="highlight.textLeading">
            {{ highlight.description }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column (Form) -->
  <div class="w-full lg:w-1/2 order-2 max-md:order-1">
    <div
      class="mx-auto font-[Inter] flex flex-col border border-gray-300 shadow-lg rounded-[30px] md:rounded-[50px] bg-white w-full max-w-[524px]"
    >
      <!-- Radial Gradient -->
      <div
        class="flex justify-center items-center w-full h-[100px] md:h-[126px] rounded-t-[30px] md:rounded-t-[50px] bg-radial-custom p-4"
      >
        <p class="text-white text-[20px] md:text-[27px] text-center">
          Login to CommesClub
        </p>
      </div>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="p-4 md:p-6">
          <div class="form-group space-y-3 md:space-y-4">
            <div class="form-field flex flex-col">
              <label class="form-label text-base md:text-lg mb-1"
                >Email *</label
              >
              <input
                type="email"
                formControlName="email"
                class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full"
                placeholder="Enter your email"
              />
            </div>

            <div class="form-field flex flex-col">
              <label class="form-label text-base md:text-lg mb-1"
                >Password *</label
              >
              <input
                type="password"
                formControlName="password"
                class="input w-full py-2 md:py-3 px-3 md:px-4 text-sm md:text-base max-w-full"
                placeholder="Enter your password"
              />
            </div>

            <!-- Error Message -->
            <div *ngIf="error" class="text-red-500 text-sm">
              {{ error }}
            </div>

            <!-- Button -->
            <div class="flex justify-center mt-4 md:mt-6">
              <button
                type="submit"
                [disabled]="!loginForm.valid || loading"
                class="btn-gradient rounded-full"
              >
                <span *ngIf="!loading">Login</span>
                <span *ngIf="loading">Logging in...</span>
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</section>
