import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';

interface FilterItem {
  label: string;
  checked: boolean;
}

interface Company {
  name: string;
  logo: string;
  industry: string;
}

interface Job {
  id: string;
  company: Company;
  type: string;
  title: string;
  description: string;
  tags: string[];
  postedAt: string;
  deadline: string;
  isNew: boolean;
  location: string;
  salary: string;
  experience: string;
}

@Component({
  selector: 'app-jobs',
  templateUrl: './jobs.component.html',
  styleUrls: ['./jobs.component.css']
})
export class JobsComponent implements OnInit, OnDestroy {
  jobs: Job[] = [];
  searchQuery = '';
  selectedFilter = 'All Jobs';
  filters = ['All Jobs', 'New Jobs', 'Featured Jobs', 'Remote Jobs'];
  
  filterTypes: FilterItem[] = [
    { label: 'Full Time', checked: false },
    { label: 'Part Time', checked: false },
    { label: 'Contract', checked: false },
    { label: 'Internship', checked: false }
  ];

  industries: FilterItem[] = [
    { label: 'Technology', checked: false },
    { label: 'Healthcare', checked: false },
    { label: 'Finance', checked: false },
    { label: 'Education', checked: false },
    { label: 'Marketing', checked: false }
  ];

  dateFilters = [
    { label: 'Last 24 hours', checked: false },
    { label: 'Last 7 days', checked: true },
    { label: 'Last 14 days', checked: false },
    { label: 'Last month', checked: false }
  ];

  isMobileFiltersOpen: boolean = false;

  constructor(private router: Router) {}

  ngOnInit() {
    // Mock data
    this.jobs = [
      {
        id: '1',
        company: {
          name: 'TechPulse',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Technology'
        },
        type: 'FULL TIME',
        title: 'Senior Frontend Developer',
        description: 'We are looking for an experienced Frontend Developer to join our dynamic team. The ideal candidate will have strong expertise in Angular and modern web technologies...',
        tags: ['Angular', 'TypeScript', 'Frontend'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
        location: 'Remote',
        salary: '$120k - $150k',
        experience: '5+ years'
      },
      {
        id: '2',
        company: {
          name: 'Health Central',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Healthcare'
        },
        type: 'FULL TIME',
        title: 'Healthcare Data Analyst',
        description: 'Join our healthcare analytics team to help transform patient care through data-driven insights...',
        tags: ['Healthcare', 'Data Analysis', 'Python'],
        postedAt: '2 days ago',
        deadline: '30 May 2025',
        isNew: true,
        location: 'New York, NY',
        salary: '$90k - $110k',
        experience: '3+ years'
      },
      {
        id: '3',
        company: {
          name: 'FinTech Solutions',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Finance'
        },
        type: 'CONTRACT',
        title: 'Blockchain Developer',
        description: 'Looking for an experienced blockchain developer to help build our next-generation financial platform...',
        tags: ['Blockchain', 'Smart Contracts', 'DeFi'],
        postedAt: '3 days ago',
        deadline: '15 June 2025',
        isNew: false,
        location: 'Remote',
        salary: '$130k - $160k',
        experience: '4+ years'
      }
    ];
  }

  applyFilter(filter: string) {
    this.selectedFilter = filter;
    this.filterJobs();
  }

  toggleFilterType(type: FilterItem) {
    type.checked = !type.checked;
    this.filterJobs();
  }

  toggleIndustry(industry: FilterItem) {
    industry.checked = !industry.checked;
    this.filterJobs();
  }

  toggleDateFilter(date: any) {
    this.dateFilters.forEach(d => d.checked = false);
    date.checked = true;
    this.filterJobs();
  }

  clearFilters() {
    this.selectedFilter = 'All Jobs';
    this.filterTypes.forEach(type => type.checked = false);
    this.industries.forEach(industry => industry.checked = false);
    this.dateFilters.forEach((date, index) => date.checked = index === 1); // Reset to "Last 7 days"
    this.searchQuery = '';
    this.filterJobs();
  }

  filterJobs() {
    // This would be replaced with actual API call in production
    let filteredJobs = [...this.jobs];

    // Filter by selected tab
    if (this.selectedFilter !== 'All Jobs') {
      switch (this.selectedFilter) {
        case 'New Jobs':
          filteredJobs = filteredJobs.filter(job => job.isNew);
          break;
        case 'Remote Jobs':
          filteredJobs = filteredJobs.filter(job => job.location.toLowerCase().includes('remote'));
          break;
        case 'Featured Jobs':
          // Add featured jobs logic here
          break;
      }
    }

    // Filter by job type
    const selectedTypes = this.filterTypes.filter(type => type.checked).map(type => type.label.toUpperCase());
    if (selectedTypes.length > 0) {
      filteredJobs = filteredJobs.filter(job => selectedTypes.includes(job.type));
    }

    // Filter by industry
    const selectedIndustries = this.industries.filter(ind => ind.checked).map(ind => ind.label);
    if (selectedIndustries.length > 0) {
      filteredJobs = filteredJobs.filter(job => selectedIndustries.includes(job.company.industry));
    }

    // Filter by search query
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filteredJobs = filteredJobs.filter(job =>
        job.title.toLowerCase().includes(query) ||
        job.company.name.toLowerCase().includes(query) ||
        job.description.toLowerCase().includes(query) ||
        job.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply date filter
    const selectedDate = this.dateFilters.find(d => d.checked);
    if (selectedDate) {
      const now = new Date();
      let cutoff = new Date();
      
      switch (selectedDate.label) {
        case 'Last 24 hours':
          cutoff.setDate(now.getDate() - 1);
          break;
        case 'Last 7 days':
          cutoff.setDate(now.getDate() - 7);
          break;
        case 'Last 14 days':
          cutoff.setDate(now.getDate() - 14);
          break;
        case 'Last month':
          cutoff.setMonth(now.getMonth() - 1);
          break;
      }
      
      // In a real application, you would compare actual dates
      // For now, we'll just keep all jobs since we're using mock data
    }

    this.jobs = filteredJobs;
  }

  refreshJobs() {
    // This would be replaced with actual API call in production
    // For now, just reset the filters
    this.clearFilters();
  }

  viewFullJob(jobId: string) {
    this.router.navigate(['/dashboard/jobs', jobId]);
  }

  applyToJob(jobId: string) {
    // Implement job application logic
    console.log('Applying to job:', jobId);
  }

  saveJob(jobId: string) {
    // Implement job saving logic
    console.log('Saving job:', jobId);
  }

  shareJob(jobId: string) {
    // Implement job sharing logic
    console.log('Sharing job:', jobId);
  }

  toggleMobileFilters(): void {
    this.isMobileFiltersOpen = !this.isMobileFiltersOpen;
    // Prevent body scroll when drawer is open
    if (this.isMobileFiltersOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }

  // Add cleanup on component destroy
  ngOnDestroy(): void {
    // Reset body overflow
    document.body.style.overflow = 'auto';
  }
} 