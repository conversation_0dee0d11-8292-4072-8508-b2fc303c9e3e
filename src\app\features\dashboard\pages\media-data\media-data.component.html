<div class="min-h-screen bg-white p-4 md:p-8">
  <!-- Header Section -->
  <div
    class="header-content flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8"
  >
    <div class="flex items-center gap-2">
      <h1 class="text-2xl font-semibold">Media Database</h1>
      <button class="text-gray-500 hover:text-gray-700">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>
    </div>
    <div class="flex items-center gap-4 w-full md:w-auto">
      <button
        class="flex items-center gap-2 text-gray-600 hover:text-gray-900 border px-3 py-2 rounded-lg"
        (click)="downloadCSV()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
          />
        </svg>
        Download CSV
      </button>
      <button
        class="px-3 py-2 bg-[#FF4D8D] text-white rounded-lg hover:bg-[#FF3D7D] transition-colors"
        (click)="uploadData()"
      >
        Upload {{ activeView === "people" ? "People" : "Companies" }}
      </button>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div class="filter-section mb-6">
    <!-- Search Bar -->
    <div class="mb-3 flex items-center gap-3">
      <div class="relative flex-1">
        <input
          type="text"
          placeholder="Search PR topics, companies, or keywords..."
          class="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF4D8D] focus:border-transparent"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-400 absolute left-3 top-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <div class="p-2.5 border border-gray-300 rounded-lg">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="icon icon-tabler icons-tabler-outline icon-tabler-sort-descending"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M4 6l9 0" />
          <path d="M4 12l7 0" />
          <path d="M4 18l7 0" />
          <path d="M15 15l3 3l3 -3" />
          <path d="M18 6l0 12" />
        </svg>
      </div>
    </div>
    <div class="filter-pills w-fit">
      <button
        [class.bg-[#0B013D]]="activeView === 'people'"
        [class.text-white]="activeView === 'people'"
        [class.border-gray-300]="activeView !== 'people'"
        [class.text-gray-700]="activeView !== 'people'"
        class="px-4 py-1 rounded-lg"
        (click)="switchView('people')"
      >
        People
      </button>
      <button
        [class.bg-[#0B013D]]="activeView === 'companies'"
        [class.text-white]="activeView === 'companies'"
        [class.border-gray-300]="activeView !== 'companies'"
        [class.text-gray-700]="activeView !== 'companies'"
        class="px-4 py-1 rounded-lg"
        (click)="switchView('companies')"
      >
        Companies
      </button>
    </div>
  </div>

  <!-- People Table -->
  <div class="table-container" *ngIf="activeView === 'people'">
    <div class="table-wrapper">
      <table class="w-full min-w-[1000px]">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              S.N
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Name
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Title
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Publication(s)
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Covers
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Info
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Socials
            </th>
            <th
              class="px-4 py-3 text-left text-sm font-medium text-gray-600"
            ></th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngIf="people.length === 0" class="hover:bg-gray-50">
            <td colspan="8" class="px-4 py-8 text-center text-gray-500">
              No results found for "{{ searchTerm }}". Try adjusting your search
              terms.
            </td>
          </tr>
          <tr
            *ngFor="let person of people; let i = index"
            class="hover:bg-gray-50"
          >
            <td class="px-4 py-4 text-sm text-gray-600">{{ i + 1 }}</td>
            <td class="px-4 py-4">
              <div class="flex items-center gap-3">
                <img
                  [src]="person.image"
                  [alt]="person.name"
                  class="w-8 h-8 rounded-full"
                />
                <span class="text-sm font-medium text-gray-900">{{
                  person.name
                }}</span>
              </div>
            </td>
            <td class="px-4 py-4 text-sm text-gray-600">{{ person.title }}</td>
            <td class="px-4 py-4 text-sm text-gray-600">
              {{ person.publications }}
            </td>
            <td class="px-4 py-4">
              <div class="flex flex-wrap gap-1">
                <span
                  *ngFor="let topic of person.covers"
                  class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded"
                >
                  {{ topic }}
                </span>
              </div>
            </td>
            <td class="px-4 py-4">
              <div class="space-y-1">
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  {{ person.phone }}
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  {{ person.email }}
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  {{ person.location }}
                </div>
              </div>
            </td>
            <td class="px-4 py-4">
              <div class="flex items-center gap-2">
                <a href="#" class="text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
                    />
                  </svg>
                </a>
                <a href="#" class="text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"
                    />
                  </svg>
                </a>
              </div>
            </td>
            <td class="px-4 py-4">
              <button class="text-gray-400 hover:text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                  />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Companies Table -->
  <div class="table-container" *ngIf="activeView === 'companies'">
    <div class="table-wrapper">
      <table class="w-full min-w-[1000px]">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              S.N
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Company
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Industry
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Size
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Founded
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Info
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
              Socials
            </th>
            <th
              class="px-4 py-3 text-left text-sm font-medium text-gray-600"
            ></th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngIf="companies.length === 0" class="hover:bg-gray-50">
            <td colspan="8" class="px-4 py-8 text-center text-gray-500">
              No results found for "{{ searchTerm }}". Try adjusting your search
              terms.
            </td>
          </tr>
          <tr
            *ngFor="let company of companies; let i = index"
            class="hover:bg-gray-50"
          >
            <td class="px-4 py-4 text-sm text-gray-600">{{ i + 1 }}</td>
            <td class="px-4 py-4">
              <div class="flex items-center gap-3">
                <img
                  [src]="company.logo"
                  [alt]="company.name"
                  class="w-8 h-8 rounded-lg"
                />
                <span class="text-sm font-medium text-gray-900">{{
                  company.name
                }}</span>
              </div>
            </td>
            <td class="px-4 py-4 text-sm text-gray-600">
              {{ company.industry }}
            </td>
            <td class="px-4 py-4 text-sm text-gray-600">{{ company.size }}</td>
            <td class="px-4 py-4 text-sm text-gray-600">
              {{ company.founded }}
            </td>
            <td class="px-4 py-4">
              <div class="space-y-1">
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                    />
                  </svg>
                  {{ company.website }}
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  {{ company.headquarters }}
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  {{ company.description }}
                </div>
              </div>
            </td>
            <td class="px-4 py-4">
              <div class="flex items-center gap-2">
                <a
                  [href]="company.socialLinks.twitter"
                  target="_blank"
                  class="text-gray-400 hover:text-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
                    />
                  </svg>
                </a>
                <a
                  [href]="company.socialLinks.linkedin"
                  target="_blank"
                  class="text-gray-400 hover:text-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"
                    />
                  </svg>
                </a>
              </div>
            </td>
            <td class="px-4 py-4">
              <button class="text-gray-400 hover:text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                  />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
