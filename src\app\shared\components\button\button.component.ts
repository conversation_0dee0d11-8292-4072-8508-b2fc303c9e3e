import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.css']
})
export class ButtonComponent {
  @Input() btnColor: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'gradient' | 'customGradient' | 'spotlight' | string = 'primary';
  @Input() btnSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'custom' = 'md';
  @Input() isDisabled: boolean = false;
  @Input() isRounded: boolean = false;
  @Input() btnVariant: 'outline' | 'solid' = 'solid';
  @Input() btnText: string = 'Click Me';
  @Input() isBlock: boolean = false;
  @Input() link: string = '';

  @Output() onBtnClick = new EventEmitter<Event>();

  get buttonClasses(): string {
    const baseClasses = 'ripple-btn'; 
    const sizeClasses = this.getSizeClasses();
    const colorClasses = this.getColorClasses();
    const variantClasses = this.getVariantClasses();
    const blockClass = this.isBlock ? 'btn-block' : '';
    const roundedClass = this.isRounded ? 'btn-rounded' : 'btn-square';

    return `${baseClasses} ${sizeClasses} ${colorClasses} ${variantClasses} ${blockClass} ${roundedClass}`;
  }

  private getSizeClasses(): string {
    switch (this.btnSize) {
      case 'xs': return 'px-2 py-1 text-xs';
      case 'sm': return 'px-3 py-2 text-sm';
      case 'md': return 'px-4 py-2 text-base';
      case 'lg': return 'px-6 py-3 text-lg';
      case 'xl': return 'px-8 py-4 text-xl';
      case 'custom': return 'px-8 py-4 sm:px-12 sm:py-5 md:px-16 md:py-6 lg:px-20 lg:py-7 xl:px-24 xl:py-8 text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl';
      default: return 'px-4 py-2 text-base';
    }
  }

  private getColorClasses(): string {
    return `btn-${this.btnColor}`;
  }

  private getVariantClasses(): string {
    return this.btnVariant === 'outline' ? 'btn-outline' : '';
  }

  onClick(event: Event): void {
    this.onBtnClick.emit(event);
  }
}




