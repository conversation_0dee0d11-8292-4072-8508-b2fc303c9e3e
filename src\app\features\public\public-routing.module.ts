import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { AboutComponent } from './about/about.component';
import { ContactComponent } from './contact/contact.component';
import { PrProfessionalsComponent } from './pr-professionals/pr-professionals.component';
import { MarketersComponent } from './marketers/marketers.component';
import { JournalistComponent } from './journalist/journalist.component';
import { FreelancersComponent } from './freelancers/freelancers.component';
import { SolutionsComponent } from './solutions/solutions.component';
import { ResourcesComponent } from './resources/resources.component';
import { ExpertDatabaseComponent } from './expert-database/expert-database.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'about', component: AboutComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'pr-professionals', component: PrProfessionalsComponent },
  { path: 'marketers', component: MarketersComponent },
  { path: 'journalist', component: JournalistComponent },
  { path: 'freelancers', component: FreelancersComponent },
  { path: 'solutions', component: SolutionsComponent },
  { path: 'resources', component: ResourcesComponent },
  { path: 'expert-database', component: ExpertDatabaseComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PublicRoutingModule {}
