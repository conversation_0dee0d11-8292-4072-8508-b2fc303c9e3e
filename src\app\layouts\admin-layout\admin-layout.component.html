<div class="flex h-screen">
  <!-- Admin <PERSON>bar Navigation -->
  <aside class="w-64 bg-indigo-900 text-white">
    <div class="p-4 bg-indigo-800">
      <h2 class="text-xl font-bold">Admin Panel</h2>
    </div>

    <nav class="mt-6">
      <ul>
        <li class="px-4 py-2 hover:bg-indigo-800">
          <a
            routerLink="/admin"
            routerLinkActive="font-bold"
            [routerLinkActiveOptions]="{ exact: true }"
            >Dashboard</a
          >
        </li>
        <li class="px-4 py-2 hover:bg-indigo-800">
          <a routerLink="/admin/user-management" routerLinkActive="font-bold"
            >User Management</a
          >
        </li>
        <li class="px-4 py-2 hover:bg-indigo-800">
          <a routerLink="/admin/reports" routerLinkActive="font-bold"
            >Reports</a
          >
        </li>
        <li class="px-4 py-2 hover:bg-indigo-800">
          <a routerLink="/admin/settings" routerLinkActive="font-bold"
            >System Settings</a
          >
        </li>
      </ul>
    </nav>

    <div class="mt-auto p-4">
      <button
        (click)="logout()"
        class="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded"
      >
        Logout
      </button>
    </div>
  </aside>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Admin Header -->
    <header class="bg-white shadow">
      <div class="flex justify-between items-center px-6 py-4">
        <h1 class="text-xl font-semibold">Admin Dashboard</h1>
        <div class="flex items-center">
          <span class="mr-2">{{ adminName }}</span>
          <!-- Admin dropdown or quick actions could go here -->
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <main class="flex-1 overflow-auto p-6 bg-gray-100">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
