<div class="min-h-screen bg-gray-50">
  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center min-h-screen">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF4D8D]"
    ></div>
  </div>

  <!-- Error State -->
  <div
    *ngIf="error"
    class="flex flex-col items-center justify-center min-h-screen p-4"
  >
    <div class="text-red-600 text-xl mb-4">Failed to load job details</div>
    <button
      (click)="goBack()"
      class="px-6 py-2 bg-[#FF4D8D] text-white rounded-lg hover:bg-[#FF3D7D] transition-colors"
    >
      Go Back
    </button>
  </div>

  <!-- Content -->
  <div *ngIf="!loading && !error && job" class="max-w-7xl mx-auto px-4 py-8">
    <!-- Back Button -->
    <button
      (click)="goBack()"
      class="mb-6 flex items-center text-gray-600 hover:text-gray-900 transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        />
      </svg>
      Back to Jobs
    </button>

    <!-- Main Content -->
    <div class="overflow-hidden">
      <div class="border rounded-lg mb-6">
        <!-- cover image -->
        <img
          src="/assets/images/eventsimg.png"
          alt=""
          class="w-full h-56 object-cover rounded-tl-lg rounded-tr-lg"
        />

        <div class="p-6">
          <!-- Job Content -->
          <div>
            <!-- Title and Type -->
            <div class="mb-6">
              <div class="flex items-center gap-2 mb-2">
                <span class="text-base text-subtitle uppercase"
                  >Journalist request</span
                >
              </div>
              <h4 class="font-semibold text-gray-900 mb-4">
                {{ job.title }}
              </h4>
            </div>

            <!-- external link -->
            <div class="flex items-center gap-2 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path
                  d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6"
                />
                <path d="M11 13l9 -9" />
                <path d="M15 4h5v5" />
              </svg>
              <a
                href="https://events.theinsurer.com/eumgasummit"
                target="_blank"
                >https://events.theinsurer.com/eumgasummit</a
              >
            </div>

            <!-- Tags -->
            <div class="flex flex-wrap gap-2 mb-6">
              <span *ngFor="let tag of job.tags" class="badge">
                {{ tag }}
              </span>
            </div>
          </div>
          <!-- company Section -->
          <div class="">
            <div class="flex flex-col gap-4">
              <!-- Company Info -->
              <div class="flex items-center gap-4 mb-2">
                <img
                  [src]="job.company.logo"
                  [alt]="job.company.name"
                  class="w-11 h-11 rounded-lg object-cover"
                />
                <div>
                  <h4 class="font-medium text-gray-900 truncate">
                    {{ job.company.name }}
                  </h4>
                  <p class="text-sm text-gray-500 truncate">
                    {{ job.company.industry }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center gap-3">
                <button
                  (click)="applyToJob()"
                  class="px-6 py-2.5 bg-gradient-primary text-white rounded-lg transition-colors flex items-center gap-2"
                >
                  Apply Now
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
                <button
                  (click)="saveJob()"
                  class="p-2.5 hover:text-black text-gray-600 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path
                      d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                    />
                    <path d="M16 19h6" />
                    <path d="M19 16v6" />
                  </svg>
                </button>
                <button
                  (click)="shareJob()"
                  class="p-2.5 hover:text-black text-gray-600 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="icon icon-tabler icons-tabler-outline icon-tabler-share-3"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path
                      d="M13 4v4c-6.575 1.028 -9.02 6.788 -10 12c-.037 .206 5.384 -5.962 10 -6v4l8 -7l-8 -7z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border p-6 rounded-lg">
        <!-- Job Description -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">
            About the Role
          </h3>
          <p class="whitespace-pre-line text-black1 text-base">
            {{ job.fullContent }}
          </p>
        </div>

        <!-- Requirements -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-black1 mb-4">Requirements</h3>
          <ul class="list-disc list-inside space-y-2 text-black1 text-base">
            <li *ngFor="let requirement of job.requirements">
              {{ requirement }}
            </li>
          </ul>
        </div>

        <!-- Responsibilities -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-black1 mb-4">
            Responsibilities
          </h3>
          <ul class="list-disc list-inside space-y-2 text-black1 text-base">
            <li *ngFor="let responsibility of job.responsibilities">
              {{ responsibility }}
            </li>
          </ul>
        </div>

        <!-- Benefits -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-black1 mb-4">Benefits</h3>
          <ul class="list-disc list-inside space-y-2 text-black1 text-base">
            <li *ngFor="let benefit of job.benefits">{{ benefit }}</li>
          </ul>
        </div>

        <p class="text-black1 text-base">
          Join Eco Ventures and be a part of a mission-driven company working
          toward a cleaner, greener future.
        </p>
      </div>
    </div>
  </div>
</div>
