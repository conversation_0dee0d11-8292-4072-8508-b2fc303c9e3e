<header class="bg-[#0A0628] text-white py-1">
  <!-- Main Header -->
  <div class="container mx-auto px-4 py-3">
    <div class="flex items-center justify-between">
    <!-- Logo -->
    <a routerLink="/" class="text-[#FF4D8D] text-2xl font-bold">
        <img src="assets/logo.png" alt="COMMSCLUB" class="w-auto h-8" />
    </a>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-8">
      <ng-container *ngFor="let item of displayNavItems">
        <a [routerLink]="item.path" 
           routerLinkActive="text-[#FF4D8D]"
           class="hover:text-[#FF4D8D] transition-colors">
          {{ item.label }}
        </a>
      </ng-container>
    </nav>

    <!-- Right Section -->
      <div class="flex items-center">
        <!-- Desktop View -->
        <div class="hidden lg:flex items-center space-x-6">
      <ng-container *ngIf="!isPublic">
        <!-- Credits -->
            <div class="flex items-center bg-[#4B3B89] rounded-full px-4 py-1.5">
          <span class="mr-2">
            <i class="fas fa-coins text-[#FF4D8D]"></i>
          </span>
              <span class="text-white text-sm">
            CREDITS LEFT
          </span>
              <span class="ml-2 bg-[#FF4D8D] px-2 py-0.5 rounded-full text-sm">
            {{ userCredits }}
          </span>
        </div>

        <!-- Notifications -->
        <button class="relative">
          <i class="fas fa-bell"></i>
          <span class="absolute -top-1 -right-1 bg-[#FF4D8D] text-xs rounded-full w-4 h-4 flex items-center justify-center">
            2
          </span>
        </button>

        <!-- User Menu -->
        <div class="relative">
          <button (click)="toggleDropdown()" class="flex items-center space-x-2">
            <img [src]="userImage" alt="User" class="w-8 h-8 rounded-full">
            <i class="fas fa-chevron-down text-xs"></i>
          </button>
          
          <!-- Dropdown Menu -->
          <div *ngIf="isDropdownOpen" 
               class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50">
            <a routerLink="/profile" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
              Profile
            </a>
            <a routerLink="/settings" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
              Settings
            </a>
            <button (click)="logout()" class="w-full text-left px-4 py-2 text-gray-800 hover:bg-gray-100">
              Logout
            </button>
          </div>
        </div>
      </ng-container>

      <!-- Public Header Actions -->
      <ng-container *ngIf="isPublic">
        <a routerLink="/auth/login" class="hover:text-[#FF4D8D] transition-colors">Login</a>
        <a routerLink="/auth/register" 
           class="bg-[#FF4D8D] text-white px-6 py-2 rounded-full hover:bg-opacity-90 transition-colors">
          Sign up Free
        </a>
      </ng-container>
        </div>

        <!-- Mobile Menu Button -->
        <button (click)="toggleMobileMenu()" class="lg:hidden p-2 ml-4">
          <svg *ngIf="!isMobileMenuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <svg *ngIf="isMobileMenuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div *ngIf="isMobileMenuOpen" class="lg:hidden bg-[#0A0628] border-t border-[#4B3B89]">
    <div class="container mx-auto px-4 py-4">
      <!-- Mobile Navigation -->
      <nav class="space-y-4">
      <ng-container *ngFor="let item of displayNavItems">
        <a [routerLink]="item.path" 
           routerLinkActive="text-[#FF4D8D]"
             class="block hover:text-[#FF4D8D] transition-colors py-2">
          {{ item.label }}
        </a>
      </ng-container>
      </nav>

      <!-- Mobile User Section -->
      <div class="mt-6 space-y-4">
        <ng-container *ngIf="!isPublic">
          <!-- Credits -->
          <div class="flex items-center bg-[#4B3B89] rounded-full px-4 py-2 justify-between">
            <div class="flex items-center">
              <span class="mr-2">
                <i class="fas fa-coins text-[#FF4D8D]"></i>
              </span>
              <span class="text-white text-sm">
                CREDITS LEFT
              </span>
            </div>
            <span class="bg-[#FF4D8D] px-2 py-0.5 rounded-full text-sm">
              {{ userCredits }}
            </span>
          </div>

          <!-- User Info -->
          <div class="flex items-center space-x-4 py-2">
            <img [src]="userImage" alt="User" class="w-10 h-10 rounded-full">
            <div class="flex-1">
              <div class="font-medium">{{ userName }}</div>
              <div class="text-sm text-gray-400">{{ userEmail }}</div>
            </div>
          </div>

          <!-- Mobile User Actions -->
          <div class="space-y-2 border-t border-[#4B3B89] pt-4">
            <a routerLink="/profile" class="block py-2 hover:text-[#FF4D8D] transition-colors">
              <i class="fas fa-user mr-2"></i> Profile
            </a>
            <a routerLink="/settings" class="block py-2 hover:text-[#FF4D8D] transition-colors">
              <i class="fas fa-cog mr-2"></i> Settings
            </a>
            <button (click)="logout()" class="w-full text-left py-2 text-red-500 hover:text-red-400 transition-colors">
              <i class="fas fa-sign-out-alt mr-2"></i> Logout
            </button>
          </div>
        </ng-container>

        <!-- Mobile Public Actions -->
        <ng-container *ngIf="isPublic">
          <div class="flex flex-col space-y-3">
            <a routerLink="/auth/login" 
               class="text-center py-2 hover:text-[#FF4D8D] transition-colors">
              Login
            </a>
            <a routerLink="/auth/register" 
               class="bg-[#FF4D8D] text-white text-center px-6 py-2 rounded-full hover:bg-opacity-90 transition-colors">
              Sign up Free
            </a>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</header>
