import { Component } from '@angular/core';

@Component({
  selector: 'app-pr-releases',
  templateUrl: './pr-releases.component.html',
  styleUrls: ['./pr-releases.component.css']
})
export class PrReleasesComponent {
  filterTypes = [
    { id: 'product-launch', label: 'Product Launch' },
    { id: 'company-news', label: 'Company News' },
    { id: 'expert-commentary', label: 'Expert Commentary' },
    { id: 'event-announcement', label: 'Event Announcement' }
  ];

  filterIndustries = [
    { id: 'tech', label: 'Tech' },
    { id: 'finance', label: 'Finance' },
    { id: 'health', label: 'Health' },
    { id: 'lifestyle', label: 'Lifestyle' },
    { id: 'education', label: 'Education' }
  ];

  filterStatus = [
    { id: 'no-credit', label: 'No pitch credit needed' },
    { id: 'recent', label: 'Most Recent (48 hours)' }
  ];

  releases = [
    {
      company: 'Health Central',
      logo: 'assets/company-logos/health-central.png',
      industry: 'Health',
      isNew: true,
      title: 'TechPulse Launches AI-Powered Email Assistant',
      description: 'TechPulse has introduced a new AI assistant for emails aimed at productivity-focused teams...',
      tags: ['AI', 'PRODUCTIVITY', 'TECHNEWS'],
      posted: 'An hour ago',
      deadline: '25 May 2025'
    },
    {
      company: 'Eco Ventures',
      logo: 'assets/company-logos/eco-ventures.png',
      industry: 'Environment',
      isNew: true,
      title: 'Eco Ventures Unveils Sustainable Packaging Solutions',
      description: 'Eco Ventures has launched a line of biodegradable packaging materials aimed at reducing plastic waste...',
      tags: ['SUSTAINABILITY', 'PACKAGING', 'GREENNEWS'],
      posted: 'An hour ago',
      deadline: '25 May 2025'
    }
  ];

  selectedType: string | null = 'company-news';
} 