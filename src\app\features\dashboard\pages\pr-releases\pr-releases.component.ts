import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';

interface FilterItem {
  label: string;
  checked: boolean;
}

interface PressRelease {
  id: string;
  company: {
    name: string;
    logo: string;
    industry: string;
  };
  type: 'PR RELEASE';
  title: string;
  description: string;
  tags: string[];
  postedAt: string;
  deadline: string;
  isNew?: boolean;
}

@Component({
  selector: 'app-pr-releases',
  templateUrl: './pr-releases.component.html',
  styleUrls: ['./pr-releases.component.css'],
})
export class PrReleasesComponent implements OnInit, OnDestroy {
  releases: PressRelease[] = [];
  selectedFilter: string = 'All';
  searchQuery: string = '';
  isMobileFiltersOpen: boolean = false;

  filters = ['All', 'My PR Releases', 'Featured', 'Saved', 'Applied'];

  filterTypes: FilterItem[] = [
    { label: 'Product Launch', checked: false },
    { label: 'Company News', checked: true },
    { label: 'Expert Commentary', checked: false },
    { label: 'Event Announcement', checked: false },
  ];

  industries: FilterItem[] = [
    { label: 'Tech', checked: false },
    { label: 'Finance', checked: false },
    { label: 'Health', checked: false },
    { label: 'Lifestyle', checked: false },
    { label: 'Education', checked: false },
  ];

  dateFilters = [
    { label: 'Last 24 hours', value: '24h', checked: false },
    { label: 'Last 7 days', value: '7d', checked: true },
    { label: 'Last 30 days', value: '30d', checked: false },
    { label: 'All time', value: 'all', checked: false },
  ];

  constructor(private router: Router) {}

  ngOnInit() {
    // Mock data
    this.releases = [
      {
        id: '1',
        company: {
          name: 'Health Central',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Health',
        },
        type: 'PR RELEASE',
        title: 'TechPulse Launches AI-Powered Email Assistant',
        description:
          'TechPulse has introduced a new AI assistant for emails aimed at productivity-focused teams...',
        tags: ['AI', 'PRODUCTIVITY', 'TECHNEWS'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
      },
      {
        id: '2',
        company: {
          name: 'Eco Ventures',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Environment',
        },
        type: 'PR RELEASE',
        title: 'Eco Ventures Unveils Sustainable Packaging Solutions',
        description:
          'Eco Ventures has launched a line of biodegradable packaging materials aimed at reducing plastic waste...',
        tags: ['SUSTAINABILITY', 'PACKAGING', 'GREENNEWS'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
      },
      {
        id: '3',
        company: {
          name: 'Eco Ventures',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Environment',
        },
        type: 'PR RELEASE',
        title: 'Eco Ventures Unveils Sustainable Packaging Solutions',
        description:
          'Eco Ventures has launched a line of biodegradable packaging materials aimed at reducing plastic waste...',
        tags: ['SUSTAINABILITY', 'PACKAGING', 'GREENNEWS'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
      },
      {
        id: '4',
        company: {
          name: 'Eco Ventures',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Environment',
        },
        type: 'PR RELEASE',
        title: 'Eco Ventures Unveils Sustainable Packaging Solutions',
        description:
          'Eco Ventures has launched a line of biodegradable packaging materials aimed at reducing plastic waste...',
        tags: ['SUSTAINABILITY', 'PACKAGING', 'GREENNEWS'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
      },
    ];
  }

  applyFilter(filter: string) {
    this.selectedFilter = filter;
    this.filterReleases();
  }

  toggleFilterType(type: FilterItem) {
    type.checked = !type.checked;
    this.filterReleases();
  }

  toggleIndustry(industry: FilterItem) {
    industry.checked = !industry.checked;
    this.filterReleases();
  }

  toggleDateFilter(date: any) {
    this.dateFilters.forEach((d) => (d.checked = false));
    date.checked = true;
    this.filterReleases();
  }

  clearFilters() {
    this.filterTypes.forEach((type) => (type.checked = false));
    this.industries.forEach((industry) => (industry.checked = false));
    this.dateFilters.forEach((date, index) => {
      date.checked = index === 1;
    });
    this.selectedFilter = 'All';
    this.searchQuery = '';
    this.filterReleases();
  }

  filterReleases() {
    // Implement filter logic based on selected filters
    // This would filter this.releases based on:
    // - selectedFilter
    // - checked filterTypes
    // - checked industries
    // - searchQuery
  }

  refreshReleases() {
    // Implement refresh logic
    // This would typically fetch fresh data from the server
  }

  applyToRelease(releaseId: string) {
    // Implement apply logic
    console.log('Applying to release:', releaseId);
  }

  viewFullRelease(releaseId: string) {
    this.router.navigate(['/dashboard/pr-releases', releaseId]);
  }

  saveRelease(releaseId: string) {
    // Implement save logic
    console.log('Saving release:', releaseId);
  }

  shareRelease(releaseId: string) {
    // Implement share logic
    console.log('Sharing release:', releaseId);
  }

  toggleMobileFilters(): void {
    this.isMobileFiltersOpen = !this.isMobileFiltersOpen;
    // Prevent body scroll when drawer is open
    if (this.isMobileFiltersOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }

  // Add cleanup on component destroy
  ngOnDestroy(): void {
    // Reset body overflow
    document.body.style.overflow = 'auto';
  }
}
