import { Component, Input, OnInit } from '@angular/core';

export type AlertType = 'success' | 'info' | 'warning' | 'error';

@Component({
  selector: 'app-alert',
  templateUrl: './alert.component.html',
  styleUrls: ['./alert.component.css'],
})
export class AlertComponent implements OnInit {
  @Input() type: AlertType = 'info';
  @Input() message: string = '';
  @Input() dismissible: boolean = true;
  @Input() autoClose: boolean = false;
  @Input() duration: number = 5000; // 5 seconds

  visible: boolean = true;
  iconClass: string = '';
  bgClass: string = '';
  textClass: string = '';
  borderClass: string = '';

  constructor() {}

  ngOnInit(): void {
    this.setClasses();

    if (this.autoClose) {
      setTimeout(() => {
        this.dismiss();
      }, this.duration);
    }
  }

  dismiss(): void {
    this.visible = false;
  }

  private setClasses(): void {
    switch (this.type) {
      case 'success':
        this.iconClass = 'text-green-500';
        this.bgClass = 'bg-green-50';
        this.textClass = 'text-green-800';
        this.borderClass = 'border-green-300';
        break;
      case 'info':
        this.iconClass = 'text-blue-500';
        this.bgClass = 'bg-blue-50';
        this.textClass = 'text-blue-800';
        this.borderClass = 'border-blue-300';
        break;
      case 'warning':
        this.iconClass = 'text-yellow-500';
        this.bgClass = 'bg-yellow-50';
        this.textClass = 'text-yellow-800';
        this.borderClass = 'border-yellow-300';
        break;
      case 'error':
        this.iconClass = 'text-red-500';
        this.bgClass = 'bg-red-50';
        this.textClass = 'text-red-800';
        this.borderClass = 'border-red-300';
        break;
      default:
        this.iconClass = 'text-blue-500';
        this.bgClass = 'bg-blue-50';
        this.textClass = 'text-blue-800';
        this.borderClass = 'border-blue-300';
    }
  }
}
