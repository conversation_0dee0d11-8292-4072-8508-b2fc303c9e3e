import {
  Component,
  AfterViewInit,
  ElementRef,
  ViewChild,
  OnInit,
  HostListener,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { filter } from 'rxjs';
gsap.registerPlugin(ScrollTrigger);
@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
})
export class HomeComponent implements AfterViewInit, OnInit {
  @ViewChild('heroText', { static: true }) heroText!: ElementRef;
  @ViewChild('parallaxImage', { static: true }) parallaxImage!: ElementRef;
  @ViewChild('parallaxText', { static: true }) parallaxText!: ElementRef;
  @ViewChild('features', { static: true }) features!: ElementRef;
  minHeight: string = '500px';
  selectedCategory: string = 'All';
  filteredCarousel: any[] = [];

  constructor(private router: Router) {
    // Force scroll to top when the component is activated
    this.router.events
      .pipe(filter((event: any) => event instanceof NavigationEnd))
      .subscribe(() => {
        window.scrollTo(0, 0);
      });
  }

  ngOnInit() {
    this.filteredCarousel = this.innerCarousel;
    this.updateMinHeight(window.innerWidth);
    console.log('🏡 HomeComponent loaded from PublicModule');
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.updateMinHeight(event.target.innerWidth);
  }

  updateMinHeight(width: number) {
    this.minHeight = width < 655 ? '720px' : '570px'; // Change minHeight based on screen size
  }

  filterCarousel(category: string) {
    this.selectedCategory = category;

    if (category === 'All') {
      this.filteredCarousel = this.innerCarousel;
    } else {
      this.filteredCarousel = this.innerCarousel;
    }

    this.buttonList = this.buttonList.map((button) => ({
      ...button,
      btnColor:
        button.btnText === this.selectedCategory
          ? 'gradient'
          : 'customGradient',
    }));
  }

  guideImages = [
    {
      content: `
    <div class="flex flex-col md:flex-row justify-around items-center gap-10 w-full">
  
      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/master-spotlight.png" alt="Mastering the Spotlight" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Mastering the Spotlight</h4>
        <h5 class="mt-4 w-4/5">5 Tactics for PR Pros to Elevate Their Sources in Indian Media</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/media-pitch.png" alt="Masala in Your Media Pitch" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Masala in Your Media Pitch</h4>
        <h5 class="mt-4 w-4/5">4 Spicy Strategies for PR Pros in India</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/stand-out.png" alt="Tune In, Stand Out" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Tune In, Stand Out</h4>
        <h5 class="mt-4 w-4/5">Connects Indian Podcasters with Star Guests</h5>
      </div>
    </div>
    `,
    },
    {
      content: `
    <div class="flex flex-col md:flex-row justify-around items-center gap-10 w-full">
  
      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/master-spotlight.png" alt="Mastering the Spotlight" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Mastering the Spotlight</h4>
        <h5 class="mt-4 w-4/5">5 Tactics for PR Pros to Elevate Their Sources in Indian Media</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/media-pitch.png" alt="Masala in Your Media Pitch" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Masala in Your Media Pitch</h4>
        <h5 class="mt-4 w-4/5">4 Spicy Strategies for PR Pros in India</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/stand-out.png" alt="Tune In, Stand Out" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Tune In, Stand Out</h4>
        <h5 class="mt-4 w-4/5">Connects Indian Podcasters with Star Guests</h5>
      </div>
    </div>
    `,
    },
    {
      content: `
    <div class="flex flex-col md:flex-row justify-around items-center gap-10 w-full">
  
      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/master-spotlight.png" alt="Mastering the Spotlight" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Mastering the Spotlight</h4>
        <h5 class="mt-4 w-4/5">5 Tactics for PR Pros to Elevate Their Sources in Indian Media</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/media-pitch.png" alt="Masala in Your Media Pitch" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Masala in Your Media Pitch</h4>
        <h5 class="mt-4 w-4/5">4 Spicy Strategies for PR Pros in India</h5>
      </div>

      <div class="bg-[#120a30] flex flex-col justify-center items-center text-white p-6 rounded-[2vw] shadow-lg text-center w-3/4">
        <img src="/assets/stand-out.png" alt="Tune In, Stand Out" class="mx-auto mb-4 w-auto h-auto object-contain" />
        <h4>Tune In, Stand Out</h4>
        <h5 class="mt-4 w-4/5">Connects Indian Podcasters with Star Guests</h5>
      </div>
    </div>
    `,
    },
  ];
  carouselImages = [
    {
      content: `
  <div class="carousel-content grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
    <div class="w-[12rem] h-[13rem] bg-[#01B0CD] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/ht.png" alt="HT" class="w-auto h-auto">
    </div>
    <div class="w-[12rem] h-[13rem] bg-[#F99E1C] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/mint.png" alt="Mint" class="w-auto h-auto">
    </div>
    <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/prmantra.png" alt="PRmantra" class="w-auto h-auto">
    </div>
    <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/paisabazaar.png" alt="Paisa Bazaar" class="w-auto h-auto">
    </div>
    <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/inc42.png" alt="Inc42" class="w-auto h-auto">
    </div>
    <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
      <img src="assets/entrackr.png" alt="Entrackr" class="w-auto h-auto">
    </div>
  </div>
  `,
    },
    {
      content: `
    <div class="carousel-content grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      <div class="w-[12rem] h-[13rem] bg-[#01B0CD] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/ht.png" alt="HT" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/prmantra.png" alt="PRmantra" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#F99E1C] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/mint.png" alt="Mint" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/paisabazaar.png" alt="Paisa Bazaar" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/inc42.png" alt="Inc42" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/entrackr.png" alt="Entrackr" class="w-auto h-auto">
      </div>
    </div>
  `,
    },
    {
      content: `
    <div class="carousel-content grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      <div class="w-[12rem] h-[13rem] bg-[#01B0CD] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/ht.png" alt="HT" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#F99E1C] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/mint.png" alt="Mint" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/prmantra.png" alt="PRmantra" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/paisabazaar.png" alt="Paisa Bazaar" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/inc42.png" alt="Inc42" class="w-auto h-auto">
      </div>
      <div class="w-[12rem] h-[13rem] bg-[#FFFFFF] overflow-hidden flex justify-center items-center rounded-xl">
        <img src="assets/entrackr.png" alt="Entrackr" class="w-auto h-auto">
      </div>
    </div>
  `,
    },
  ];

  innerCarousel = [
    {
      content: `
  <div class="relative font-[Inter] mx-auto mt-[6.5rem] p-6 bg-[#0c052b] text-white rounded-[3vw] text-center shadow-lg">

    <div class="absolute -top-8 left-8">
      <img src ="assets/quotes-top.png" alt="Quote" class="w-16 h-16">
    </div>

    <div class="absolute -top-[30%] left-1/2 transform -translate-x-1/2">
      <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
    </div>

    <p class="mt-16 lg:mt-24 md:mt-20 font-normal">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
    </p>

    <div class="mt-[50px]">
      <p class="font-normal text-[25px]">Mr. John</p>
      <p class="text-[17px]">Barry Maher & Associates</p>
    </div>

    <div class="absolute -bottom-8 right-8">
      <img src ="assets/quotes-bottom.png" alt="Quote" class="w-[3.8rem] h-16">
    </div>
  </div>
  `,
    },
    {
      content: `
    <div class="relative font-[Inter] mx-auto mt-[6.5rem] p-6 bg-[#0c052b] text-white rounded-[3vw] text-center shadow-lg">
  
      <div class="absolute -top-8 left-8">
        <img src ="assets/quotes-top.png" alt="Quote" class="w-16 h-16">
      </div>
  
      <div class="absolute -top-[30%] left-1/2 transform -translate-x-1/2">
        <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
      </div>
  
      <p class="mt-16 lg:mt-24 md:mt-20 font-normal">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
      </p>

      <div class="mt-[50px]">
        <p class="font-normal text-[25px]">Mr. John</p>
        <p class="text-[17px]">Barry Maher & Associates</p>
      </div>
  
      <div class="absolute -bottom-8 right-8">
        <img src ="assets/quotes-bottom.png" alt="Quote" class="w-[3.8rem] h-16">
      </div>
    </div>
  `,
    },
    {
      content: `
      <div class="relative font-[Inter] mx-auto mt-[6.5rem] p-6 bg-[#0c052b] text-white rounded-[3vw] text-center shadow-lg">
    
        <div class="absolute -top-8 left-8">
          <img src ="assets/quotes-top.png" alt="Quote" class="w-16 h-16">
        </div>
    
        <div class="absolute -top-[30%] left-1/2 transform -translate-x-1/2">
          <img src="assets/carouselImage.png" alt="User" class="w-auto h-auto rounded-full border-4 shadow-[0_0_40px_0px_#0C023E40] border-white bg-white">
        </div>
    
        <p class="mt-16 lg:mt-24 md:mt-20 font-normal">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
        </p>
  
        <div class="mt-[50px]">
          <p class="font-normal text-[25px]">Mr. John</p>
          <p class="text-[17px]">Barry Maher & Associates</p>
        </div>
    
        <div class="absolute -bottom-8 right-8">
          <img src ="assets/quotes-bottom.png" alt="Quote" class="w-[3.8rem] h-16">
        </div>
      </div>
  `,
    },
  ];

  categories = [
    'Business & Finance',
    'Technology & Innovation',
    'Health & Wellness',
    'Politics & Government',
    'Entertainment & Culture',
    'Science & Environment',
    'Sports & Recreation',
    'Education & Learning',
    'Lifestyle & Travel',
    'Opinion & Editorial',
    'Real Estate & Property',
    'Food & Beverage',
    'Automotive & Transportation',
    'Fashion & Beauty',
    'Social Issues & Activism',
  ];

  cards = [
    {
      imgSrc: 'assets/public-speaking.png',
      heading: 'PR Professionals',
      description:
        'Get access to a wide range of coverage opportunities and easily identify the one that is most suitable for your clients.',
      link: '/pr-professionals',
    },
    {
      imgSrc: 'assets/content-strategy.png',
      heading: 'Digital Marketers',
      description:
        'Boost your digital PR campaigns with information about media contacts and exclusive coverage opportunities.',
      link: '/marketers',
    },
    {
      imgSrc: 'assets/press-pass.png',
      heading: 'Journalist',
      description:
        'Don’t ever miss a deadline, get comments and resources instantly, and stay updated with all that you want to cover.',
      link: '/journalist',
    },
    {
      imgSrc: 'assets/freelancer.png',
      heading: 'Freelancers',
      description:
        'Connect with professionals from PR agencies, contribute to features, and promote your work with the network of editors.',
      link: '/freelancers',
    },
  ];

  buttonList = [
    { btnText: 'All', btnColor: 'gradient' },
    { btnText: 'PR Professionals', btnColor: 'customGradient' },
    { btnText: 'Digital Marketers', btnColor: 'customGradient' },
    { btnText: 'Journalist', btnColor: 'customGradient' },
    { btnText: 'Freelancers', btnColor: 'customGradient' },
  ];

  ngAfterViewInit() {
    // ✅ Fade-in animation for Hero Section
    gsap.from(this.heroText.nativeElement, {
      opacity: 0,
      y: 50,
      duration: 1.5,
      ease: 'power2.out',
    });

    // ✅ Parallax Effect for Image
    gsap.to(this.parallaxImage.nativeElement, {
      y: '-50%', // Moves up as you scroll
      ease: 'none',
      scrollTrigger: {
        trigger: this.parallaxImage.nativeElement,
        start: 'top bottom',
        end: 'bottom top',
        scrub: 1,
      },
    });

    // ✅ Scroll Fade-in for Parallax Text
    gsap.fromTo(
      this.parallaxText.nativeElement,
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        scrollTrigger: {
          trigger: this.parallaxText.nativeElement,
          start: 'top 70%',
          end: 'center center',
          scrub: true,
        },
      }
    );

    // ✅ Features Section Animation
    gsap.utils
      .toArray(this.features.nativeElement.children)
      .forEach((card: any) => {
        gsap.fromTo(
          card,
          { scale: 0.8, opacity: 0.5 },
          {
            scale: 1.1,
            opacity: 1,
            scrollTrigger: {
              trigger: card,
              start: 'top 75%',
              end: 'center center',
              scrub: true,
            },
          }
        );
      });
  }
}
