import { Component } from '@angular/core';
interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

@Component({
  selector: 'app-security-settings',
  templateUrl: './security-settings.component.html',
  styleUrls: ['./security-settings.component.css'],
})
export class SecuritySettingsComponent {
  notificationSettings: NotificationSetting[] = [
    {
      id: 'push',
      title: 'Reset Password',
      description:
        'Get instant notifications on your desktop for real-time updates.',
      enabled: true,
    },
    {
      id: 'marketing',
      title: 'Deactivate Account',
      description: 'Receive updates about new features, promotions, and news.',
      enabled: false,
    },
    {
      id: 'security',
      title: 'Logout of all Emails',
      description:
        'Get notified about important security-related activities on your account.',
      enabled: true,
    },
  ];

  toggleNotification(setting: NotificationSetting): void {
    setting.enabled = !setting.enabled;
    // Implement notification settings update logic here
    console.log('Notification setting updated:', setting);
  }

  saveSettings(): void {
    // Implement save settings logic here
    console.log('Saving notification settings:', this.notificationSettings);
  }
}
