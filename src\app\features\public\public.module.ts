import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HomeComponent } from './home/<USER>';
import { AboutComponent } from './about/about.component';
import { ContactComponent } from './contact/contact.component';
import { SharedModule } from '../../shared/shared.module';
import { PublicRoutingModule } from './public-routing.module';
import { FreelancersComponent } from './freelancers/freelancers.component';
import { JournalistComponent } from './journalist/journalist.component';
import { MarketersComponent } from './marketers/marketers.component';
import { PrProfessionalsComponent } from './pr-professionals/pr-professionals.component';
import { SolutionsComponent } from './solutions/solutions.component';
import { ResourcesComponent } from './resources/resources.component';
import { ExpertDatabaseComponent } from './expert-database/expert-database.component';

@NgModule({
  declarations: [
    HomeComponent,
    AboutComponent,
    ContactComponent,
    FreelancersComponent,
    JournalistComponent,
    MarketersComponent,
    PrProfessionalsComponent,
    SolutionsComponent,
    ResourcesComponent,
    ExpertDatabaseComponent,
  ],
  imports: [CommonModule, PublicRoutingModule, SharedModule],
})
export class PublicModule {}
