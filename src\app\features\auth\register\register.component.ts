import { Component } from '@angular/core';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
})
export class RegisterComponent {
  highlights = [
    {
      title: 'Read Receipts',
      description:
        "Say goodbye to pitching into the void. Our network instantly notifies you when media professionals read your pitch or view your source's profile.",
      image: 'assets/showcase.png',
      gap: 'gap-[49px]',
      textLeading: 'leading-[29px]',
    },
    {
      title: 'Pitch Intelligence',
      description:
        "Quickly identify high-priority media requests with less competition using CommsClub's live reporter engagement and network activity data.",
      image: 'assets/expand.png',
      gap: 'gap-[35px]',
      textLeading: 'leading-[30px]',
    },
    {
      title: 'Topical Alerts',
      description:
        "Our advanced matching technology notifies you of real-time media opportunities that align with your sources' specific expertise.",
      image: 'assets/support.png',
      gap: 'gap-[40px]',
      textLeading: 'leading-[29px]',
    },
    {
      title: 'Expert Profiles',
      description:
        'Create free profiles for your thought leaders and let media professionals approach you with targeted opportunities.',
      image: 'assets/media.png',
      gap: 'gap-[28.9px]',
      textLeading: 'leading-[29px]',
    },
  ];
}
