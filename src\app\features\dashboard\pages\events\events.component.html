<!-- Main Container -->
<div class="min-h-screen bg-gray-50">
  <!-- Top Header Section - Full Width -->
  <div class="bg-white border-b border-gray-200">
    <div class="p-4 md:p-6">
      <!-- Title and Add Button -->
      <div
        class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"
      >
        <div class="flex items-center gap-2">
          <h1 class="text-xl md:text-2xl font-semibold">Events</h1>
          <button class="text-gray-500 hover:text-gray-700">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </button>
        </div>
        <button
          class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg flex items-center justify-center gap-2 hover:bg-[#FF3D7D] transition-colors"
          routerLink="add-event"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Event
        </button>
      </div>

      <!-- Filter Tabs -->
      <div class="mb-6 overflow-x-auto">
        <div class="flex flex-row gap-2 pt-1">
          <div>
            <button
              *ngFor="let filter of filters"
              (click)="selectedFilter = filter"
              [class.text-[#FFFFFF]]="selectedFilter === filter"
              [class.bg-[#092236]]="selectedFilter === filter"
              class="mr-2 max-md: mb-2 px-4 md:px-5 py-1 text-sm font-medium transition-colors border rounded-full"
            >
              {{ filter }}
            </button>
          </div>
          <div>
            <button
              (click)="refreshEvents()"
              class="text-[#FF4D8D] hover:text-[#FF3D7D] flex flex-row gap-2 items-center px-4 md:px-5 py-1 text-sm font-medium transition-colors border rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span class="hidden sm:inline">Refresh</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="mb-3 flex items-center gap-3">
        <div class="flex-1 flex items-center gap-3">
          <div class="relative flex-1">
            <input
              type="text"
              [(ngModel)]="searchQuery"
              placeholder="Search events, companies, or keywords..."
              class="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF4D8D] focus:border-transparent"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400 absolute left-3 top-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <div class="p-2.5 border border-gray-300 rounded-lg">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon icon-tabler icons-tabler-outline icon-tabler-sort-descending"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M4 6l9 0" />
              <path d="M4 12l7 0" />
              <path d="M4 18l7 0" />
              <path d="M15 15l3 3l3 -3" />
              <path d="M18 6l0 12" />
            </svg>
          </div>
        </div>
        <!-- Mobile Filter Button -->
        <button
          (click)="isMobileFiltersOpen = true"
          class="md:hidden p-2.5 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Area with Sidebar -->
  <div class="flex flex-col md:flex-row">
    <!-- Mobile Filter Overlay -->
    <div
      class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
      [class.opacity-100]="isMobileFiltersOpen"
      [class.opacity-0]="!isMobileFiltersOpen"
      [class.pointer-events-auto]="isMobileFiltersOpen"
      [class.pointer-events-none]="!isMobileFiltersOpen"
      (click)="isMobileFiltersOpen = false"
    ></div>

    <!-- Desktop Sidebar - Visible on md and up -->
    <div
      class="hidden md:block w-64 lg:w-72 bg-white p-6 border-r border-gray-200"
    >
      <div class="sticky top-[80px]">
        <!-- Filter by Type -->
        <div class="mb-4 border rounded-lg">
          <div
            class="px-[1rem] py-[0.5rem] rounded-tl-lg rounded-tr-lg bg-primary-50"
          >
            <h4 class="text-sm font-medium text-gray-700">Filter by Type</h4>
          </div>
          <div class="space-y-3 px-[1rem] py-[0.5rem]">
            <div *ngFor="let type of filterTypes" class="flex items-center">
              <input
                type="checkbox"
                [id]="'desktop-' + type.label"
                [checked]="type.checked"
                (change)="filterEvents()"
                class="w-4 h-4 text-[#FF4D8D] border-gray-300 rounded focus:ring-[#FF4D8D]"
              />
              <label
                [for]="'desktop-' + type.label"
                class="ml-2 text-sm text-gray-600"
                >{{ type.label }}</label
              >
            </div>
          </div>
        </div>

        <!-- Filter by Industry -->
        <div class="mb-6 border rounded-lg">
          <div
            class="px-[1rem] py-[0.5rem] rounded-tl-lg rounded-tr-lg bg-primary-50"
          >
            <h4 class="text-sm font-medium text-gray-700">
              Filter by Industry
            </h4>
          </div>
          <div class="space-y-3 px-[1rem] py-[0.5rem]">
            <div *ngFor="let industry of industries" class="flex items-center">
              <input
                type="checkbox"
                [id]="'desktop-' + industry.label"
                [checked]="industry.checked"
                (change)="filterEvents()"
                class="w-4 h-4 text-[#FF4D8D] border-gray-300 rounded focus:ring-[#FF4D8D]"
              />
              <label
                [for]="'desktop-' + industry.label"
                class="ml-2 text-sm text-gray-600"
                >{{ industry.label }}</label
              >
            </div>
          </div>
        </div>

        <!-- Clear Filters Button -->
        <button
          (click)="clearFilters()"
          class="w-full px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Clear All Filters
        </button>
      </div>
    </div>

    <!-- Mobile Filter Drawer - Only shown on mobile -->
    <div
      class="md:hidden fixed inset-y-0 left-0 w-[280px] bg-white p-4 border-r border-gray-200 transform transition-transform duration-300 ease-in-out z-50 overflow-y-auto"
      [class.translate-x-0]="isMobileFiltersOpen"
      [class.translate-x-[-100%]]="!isMobileFiltersOpen"
    >
      <!-- Mobile Close Button -->
      <button
        (click)="isMobileFiltersOpen = false"
        class="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <div class="pt-2">
        <h3 class="font-medium text-gray-900 mb-4">Filters</h3>

        <!-- Mobile Filter Content -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Event Type</h4>
          <div class="space-y-3">
            <div *ngFor="let type of filterTypes" class="flex items-center">
              <input
                type="checkbox"
                [id]="'mobile-' + type.label"
                [checked]="type.checked"
                (change)="filterEvents()"
                class="w-4 h-4 text-[#FF4D8D] border-gray-300 rounded focus:ring-[#FF4D8D]"
              />
              <label
                [for]="'mobile-' + type.label"
                class="ml-2 text-sm text-gray-600"
                >{{ type.label }}</label
              >
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Industry</h4>
          <div class="space-y-3">
            <div *ngFor="let industry of industries" class="flex items-center">
              <input
                type="checkbox"
                [id]="'mobile-' + industry.label"
                [checked]="industry.checked"
                (change)="filterEvents()"
                class="w-4 h-4 text-[#FF4D8D] border-gray-300 rounded focus:ring-[#FF4D8D]"
              />
              <label
                [for]="'mobile-' + industry.label"
                class="ml-2 text-sm text-gray-600"
                >{{ industry.label }}</label
              >
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Date</h4>
          <div class="space-y-3">
            <div *ngFor="let date of dateFilters" class="flex items-center">
              <input
                type="radio"
                [id]="'mobile-' + date.value"
                name="mobileDateFilter"
                [checked]="date.checked"
                (change)="filterEvents()"
                class="w-4 h-4 text-[#FF4D8D] border-gray-300 focus:ring-[#FF4D8D]"
              />
              <label
                [for]="'mobile-' + date.value"
                class="ml-2 text-sm text-gray-600"
                >{{ date.label }}</label
              >
            </div>
          </div>
        </div>

        <!-- Mobile Clear Filters Button -->
        <button
          (click)="clearFilters()"
          class="w-full px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Clear All Filters
        </button>
      </div>
    </div>

    <!-- Events Grid -->
    <div class="flex-1 p-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div
          *ngFor="let event of events"
          class="bg-white border border-gray-200 rounded-lg flex flex-col h-full"
        >
          <!-- Event Banner Image -->
          <div class="relative h-64 w-full">
            <img
              [src]="
                event.bannerImage || 'assets/images/default-event-banner.jpg'
              "
              [alt]="event.title"
              class="w-full h-full object-cover rounded-t-lg"
            />
            <!-- Online Status Badge -->
            <div
              *ngIf="event.isOnline"
              class="absolute top-4 right-4 bg-[#1a1f3d] text-white text-xs px-3 py-1 rounded-full"
            >
              ONLINE
            </div>
          </div>

          <!-- Event Content -->
          <div class="p-6">
            <!-- Date and Attendees -->
            <div
              class="flex items-center justify-between text-sm text-gray-600 mb-3"
            >
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                {{ event.eventDate }}
              </div>
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
                {{ event.attendees || 0 }} attendees
              </div>
            </div>

            <!-- Event Title -->
            <h2 class="text-xl font-semibold text-gray-900 mb-1">
              {{ event.title }}
            </h2>
            <h5 class="text-lg font-medium text-gray-900">
              European MGA Market & Insurance Innovation
            </h5>
            <p class="text-subtitle text-sm">
              Shaping the Future of Specialty Insurance
            </p>

            <!-- Tags -->
            <div class="flex flex-wrap gap-2 mb-4 mt-4">
              <span *ngFor="let tag of event.tags" class="badge">
                {{ tag }}
              </span>
            </div>

            <!-- Company Info -->
            <div class="flex items-center gap-3 mb-6">
              <img
                [src]="event.company.logo"
                [alt]="event.company.name"
                class="w-10 h-10 rounded-lg object-cover"
              />
              <div>
                <h3 class="text-lg font-medium text-gray-900">
                  {{ event.company.name }}
                </h3>
                <p class="text-sm text-subtitle">
                  {{ event.company.industry }}
                </p>
              </div>
            </div>

            <!-- Actions -->
            <!-- Action Buttons -->
            <div class="flex items-center gap-3">
              <button
                (click)="viewEventDetails(event.id)"
                class="flex-1 px-4 py-[6px] border-2 font-semibold border-primary rounded-lg transition-colors text-center text-sm"
              >
                View Details
              </button>
              <button
                class="flex-1 px-4 py-2 bg-gradient-primary text-white rounded-lg hover:bg-[#FF3D7D] transition-colors text-center text-sm"
              >
                Attend
              </button>
              <div class="flex items-center gap-1">
                <button
                  class="p-2 hover:text-black text-gray-600 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path
                      d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                    />
                    <path d="M16 19h6" />
                    <path d="M19 16v6" />
                  </svg>
                </button>
                <button
                  class="p-2 hover:text-black text-gray-600 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="icon icon-tabler icons-tabler-outline icon-tabler-share-3"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path
                      d="M13 4v4c-6.575 1.028 -9.02 6.788 -10 12c-.037 .206 5.384 -5.962 10 -6v4l8 -7l-8 -7z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
