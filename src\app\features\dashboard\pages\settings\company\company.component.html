<div class="settings-container">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <button class="text-xl font-semibold">
      <i class="fas fa-chevron-left"></i>
      Company
    </button>
    <button
      class="text-sm px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
    >
      Edit Company Details
    </button>
  </div>

  <!-- Content -->
  <div class="settings-content">
    <!-- Profile Card -->
    <div class="bg-gradient-primary rounded-2xl p-6 text-white mb-8">
      <div class="flex items-center gap-4">
        <div class="relative">
          <img
            [src]="user.avatar"
            alt="Profile"
            class="w-20 h-20 rounded-sm object-cover"
          />
        </div>
        <div>
          <h3 class="text-2xl font-semibold mb-1">Health Central</h3>
          <p class="text-base">Health</p>
        </div>
      </div>
    </div>
    <div class="tabs-nav w-fit">
      <button
        class="tab-item"
        [class.active]="activeTab === 'overview'"
        (click)="setActiveTab('overview')"
      >
        Overview
      </button>
      <button
        class="tab-item"
        [class.active]="activeTab === 'credit-activity'"
        (click)="setActiveTab('credit-activity')"
      >
        Experts
      </button>
      <button
        class="tab-item"
        [class.active]="activeTab === 'invoices'"
        (click)="setActiveTab('invoices')"
      >
        Team Members
      </button>
    </div>
    <!-- Tabs -->

    <!-- Overview Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'overview'">
      <!-- Basic Info -->
      <div class="info-card">
        <div class="info-grid px-[1rem] py-[1rem]">
          <div class="info-item">
            <span class="info-label">Name</span>
            <span class="info-value">Health Central</span>
          </div>
          <div class="info-item">
            <span class="info-label">CEO</span>
            <span class="info-value">Linda Chiem</span>
          </div>
          <div class="info-item">
            <span class="info-label">Contact Person</span>
            <span class="info-value">Linda Chiem</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email</span>
            <span class="info-value"><EMAIL></span>
          </div>
          <div class="info-item">
            <span class="info-label">Contact Number</span>
            <span class="info-value">+44 **********</span>
          </div>
          <div class="info-item">
            <span class="info-label">Address</span>
            <span class="info-value">Leeds, UK</span>
          </div>
          <div class="info-item">
            <span class="info-label">Socials</span>
            <div class="flex flex-row gap-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-brand-linkedin"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M8 11v5" />
                <path d="M8 8v.01" />
                <path d="M12 16v-5" />
                <path d="M16 16v-3a2 2 0 1 0 -4 0" />
                <path
                  d="M3 7a4 4 0 0 1 4 -4h10a4 4 0 0 1 4 4v10a4 4 0 0 1 -4 4h-10a4 4 0 0 1 -4 -4z"
                />
              </svg>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-brand-twitter"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path
                  d="M22 4.01c-1 .49 -1.98 .689 -3 .99c-1.121 -1.265 -2.783 -1.335 -4.38 -.737s-2.643 2.06 -2.62 3.737v1c-3.245 .083 -6.135 -1.395 -8 -4c0 0 -4.182 7.433 4 11c-1.872 1.247 -3.739 2.088 -6 2c3.308 1.803 6.913 2.423 10.034 1.517c3.58 -1.04 6.522 -3.723 7.651 -7.742a13.84 13.84 0 0 0 .497 -3.753c0 -.249 1.51 -2.772 1.818 -4.013z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Credit Activity Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'credit-activity'">
      <!-- Experts Grid -->
      <div class="flex-1 p-0">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <div
            *ngFor="let event of events"
            class="bg-white border border-gray-200 rounded-lg transition-shadow flex flex-col h-full"
          >
            <!-- Event Banner Image -->
            <div class="relative h-58 w-full">
              <img
                src="http://localhost:4200/assets/images/eventsimg.png"
                alt="img"
                class="w-full h-full object-cover rounded-t-lg"
              />
            </div>

            <!-- Event Content -->
            <div class="p-4">
              <!-- Event Title -->
              <h2 class="text-lg font-semibold text-gray-900 mb-1">
                Sagnik Roy
              </h2>
              <p class="text-[#7B7B7B] text-sm mb-3">
                Senior Vice President at Health Central
              </p>

              <!-- Actions -->
              <span class="badge"> marketing </span>
              <!-- Action Buttons -->
              <div class="flex items-center gap-3 mt-4">
                <button
                  class="flex-1 px-4 py-[6px] border-2 font-semibold border-primary rounded-lg transition-colors text-center text-sm"
                >
                  Contact
                </button>

                <div class="flex items-center gap-2">
                  <button
                    class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                      />
                      <path d="M16 19h6" />
                      <path d="M19 16v6" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoices Tab Content -->
    <div class="tab-content" *ngIf="activeTab === 'invoices'">
      <!-- Experts Grid -->
      <div class="flex-1">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <div
            *ngFor="let event of TeamMembers"
            class="bg-white border border-gray-200 rounded-lg transition-shadow flex flex-col h-full"
          >
            <!-- Event Banner Image -->
            <div class="flex flex-row items-center gap-2 px-3">
              <div class="relative">
                <img
                  src="http://localhost:4200/assets/images/eventsimg.png"
                  alt="img"
                  class="w-24 h-24 min-w-24 max-w-24 min-h-24 max-h-24 rounded-full"
                />
              </div>

              <!-- Event Content -->
              <div class="p-4">
                <!-- Event Title -->
                <h2 class="text-lg font-semibold text-gray-900 mb-1">
                  Sagnik Roy
                </h2>
                <p class="text-subtitle text-sm mb-3">
                  Senior Vice President at Health Central
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
