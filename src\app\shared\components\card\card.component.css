.card {
  border-radius: 2rem;
  box-shadow: 0px 0px 30px 0px #0C023E26;
  overflow: hidden;
  text-align: center;
  background-color: #e4ebfd;
}

.card:hover {
    background-color: white; 
  }

.card:hover .card-img-container {
    background: radial-gradient(50% 50% at 100% 0%, #F42447 59.03%, #F5327D 100%);
}

.card-img-container {
  background-color: #0c023e;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 15rem;
}

.card-img-container img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}

/* Mobile view adjustments */
@media screen and (max-width: 768px) {

  .card {
    border-radius: 1rem;
    box-shadow: 0px 0px 20px 0px #0C023E26;
  }
  .card-img-container {
    height: 8rem;
    padding: 1.5rem;
  }

  .card-img-container img {
    max-width: 80%;
    max-height: 80%;
  }
}

.card-content {
  padding: 10px;
}

.card-content h3 {
  margin: 20px 0;
  font-size: 1.625em;
}

.card-content p {
  color: #212121;
  font-size: 1.125rem;
  width: 15rem;
  margin: 20px 0;
}