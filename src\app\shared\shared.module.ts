import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Components
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { AlertComponent } from './components/alert/alert.component';
import { LoaderComponent } from './components/loader/loader.component';
import { PaginationComponent } from './components/pagination/pagination.component';
import { CardComponent } from './components/card/card.component';
import { LinkComponent } from './components/link/link.component';
import { ButtonComponent } from './components/button/button.component';
import { CarouselComponent } from './components/carousel/carousel.component';
import { FormComponent } from './components/form/form.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { StepperComponent } from './components/stepper/stepper.component';

// Pipes
// Add custom pipes here

// Directives
// Add custom directives here

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    AlertComponent,
    LoaderComponent,
    PaginationComponent,
    CardComponent,
    LinkComponent,
    ButtonComponent,
    CarouselComponent,
    FormComponent,
    NotFoundComponent,
    StepperComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    SlickCarouselModule,
  ],
  exports: [
    // Export modules
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,

    // Export components
    HeaderComponent,
    FooterComponent,
    AlertComponent,
    LoaderComponent,
    PaginationComponent,
    CardComponent,
    LinkComponent,
    ButtonComponent,
    CarouselComponent,
    FormComponent,
    NotFoundComponent,
    StepperComponent,
  ],
})
export class SharedModule {}
