import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { QuillModule } from 'ngx-quill';

import { JobsComponent } from './jobs.component';
import { JobDetailComponent } from './job-detail/job-detail.component';
import { AddPostComponent } from './add-post/add-post.component';

@NgModule({
  declarations: [JobsComponent, JobDetailComponent, AddPostComponent],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    SharedModule,
    QuillModule.forRoot(),
  ],
})
export class JobsModule {}
