<!-- Main Content Area -->
<main class="content-container">
  <!-- Hero Section -->
  <section>
    <div
      class="w-full min-h-[20rem] md:h-[28.6rem] flex flex-col font-normal font-[Inter] justify-center items-center bg-gradient-to-r from-[#0B013D] via-[#342c52] to-[#0B013D] px-4"
    >
      <div
        class="w-full max-w-[71rem] flex flex-col items-center text-white text-center gap-[15px] py-8 md:py-0"
      >
        <h3 class="text-[16px] md:text-[20px] leading-normal md:leading-[50px] mt-[40px] md:mt-[80px] tracking-[2px] md:tracking-[2.8px] px-2">
          SMALL BUSINESSES | PERSONAL BRANDS | SELF-REPRESENTING EXPERTS
        </h3>
        <h1 class="text-[32px] md:text-[45px] leading-[1.2] md:leading-[85px] px-4">
          Elevate Your Brand by Establishing Your Authority
        </h1>
        <p class="text-[16px] md:text-[20px] leading-[1.6] md:leading-[32px] w-full md:w-[70%] px-4">
          Join CommsClub's global network of experts and forge meaningful
          connections with media professionals covering your industry.
        </p>
      </div>
    </div>
  </section>

  <!-- buttons -->
  <section>
    <div
      class="flex flex-wrap justify-center items-center gap-[15px] md:gap-[20px] mt-[30px] md:mt-[50px] mb-[30px] md:mb-[50px] px-4"
    >
      <app-button
        *ngFor="let button of buttonList"
        [btnText]="button.btnText"
        [btnColor]="button.btnColor"
        [link]="button.link"
      >
      </app-button>
    </div>
  </section>

  <!-- Images and forms -->
  <section
    class="w-full flex flex-col lg:flex-row justify-around py-6 md:py-10 bg-white min-h-[700px] relative px-4 md:px-8 gap-8"
  >
    <!-- Left Column (Images + Text) -->
    <div class="w-full lg:w-1/2 flex flex-col items-start text-left">
      <h3 class="px-2 md:px-6 text-[24px] md:text-[30px] leading-[1.2] mb-4">
        Amplify Your Presence with CommsClub
      </h3>
      <div class="space-y-4 md:space-y-6 p-2 md:p-6 font-[Inter]">
        <div
          *ngFor="let highlight of highlights"
          class="flex flex-col md:flex-row items-start md:items-center gap-4 md:gap-6"
        >
          <img
            [src]="highlight.image"
            [alt]="highlight.title"
            class="w-auto h-auto"
          />
          <div class="flex flex-col gap-2 md:gap-4">
            <p class="text-[#F3254D] leading-none tracking-normal text-[18px] md:text-[22px]">
              {{ highlight.title }}
            </p>
            <p class="text-[16px] md:text-[18px]" [ngClass]="highlight.textLeading">
              {{ highlight.description }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column (Form) -->
    <div class="w-full lg:w-1/2">
      <app-form></app-form>
    </div>
  </section>

  <!-- How it works -->
  <section class="px-4 py-8 md:py-12">
    <div class="flex justify-center font-[Inter] mb-6 md:mb-8">
      <p class="text-[28px] md:text-[39px] text-[#180F47] text-center leading-[1.2]">How it Works</p>
    </div>
    <div class="w-full grid gap-8 place-items-center my-[20px] md:my-[40px] font-[Inter]">
      <div
        *ngFor="let feature of workContent; let i = index"
        class="flex flex-col lg:flex-row justify-center items-center gap-6 lg:gap-[10vw] w-full px-4 md:px-0 lg:w-[65%]"
        [class.lg:flex-row-reverse]="i % 2 === 0"
      >
        <div class="w-full lg:w-1/2">
          <img
            [src]="feature.image"
            [alt]="feature.title"
            class="w-full h-auto object-contain max-w-full mx-auto"
          />
        </div>
        <div class="flex flex-col gap-4 md:gap-6 w-full lg:w-1/2 text-center lg:text-left">
          <h3 class="text-[24px] md:text-[30px] text-[#0B013D] leading-[1.3] md:leading-[38px]">
            {{ feature.title }}
          </h3>
          <p class="text-[16px] md:text-xl text-[#555555] leading-[1.6]">{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Expert Directory -->
  <section class="flex flex-col justify-center items-center gap-[20px] md:gap-[27px] px-4 py-8 md:py-12">
    <div
      class="flex flex-col justify-center items-center font-[Inter] gap-[20px] md:gap-[27px]"
    >
      <p class="text-[20px] md:text-[25px] text-[#F3264F]">Expert Directory</p>
      <p class="text-[24px] md:text-[33px] text-[#180F47] text-center px-4 leading-[1.2]">
        Build Strong Connection for Better Stories
      </p>
      <p class="text-[16px] md:text-[18px] leading-[1.6] w-full md:w-[61.5rem] text-center px-4">
        CommsConnect empowers storytellers to expand their network of sources,
        experts, guests, and speakers. <br class="hidden md:block" />
        Explore our expert database and start building connections today.
      </p>
      <div
        class="w-full md:w-[90%] grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 p-4 md:p-6"
      >
        <div
          *ngFor="let category of categories"
          class="p-4 md:p-8 flex justify-center hover-gradient items-center border border-[#F53359] rounded-[12px] md:rounded-[1vw] shadow-[0_0_30px_0px_#0C023E26] text-[16px] md:text-[20px] text-center min-h-[80px] md:min-h-[100px]"
        >
          {{ category }}
        </div>
      </div>
    </div>
    <app-button
      btnColor="gradient"
      btnSize="md"
      [isDisabled]="false"
      [isRounded]="true"
      btnText="Explore Categories"
      class="mt-4 md:mt-6"
    >
    </app-button>
  </section>

  <!-- Testimonials -->
  <section class="mt-[50px] md:mt-[100px] mb-[22px] px-4">
    <div class="flex flex-col justify-center items-center font-[Inter] gap-[40px] md:gap-[78px]">
      <h1 class="text-[28px] md:text-[39px] leading-[1.2] text-center">What People About Say</h1>
      <div class="w-full md:w-1/2 flex justify-center items-center">
        <app-carousel
          [slides]="innerCarousel"
          [dotColor]="'#F53076'"
          [minHeight]="'570px'"
        ></app-carousel>
      </div>
    </div>
  </section>
</main>
