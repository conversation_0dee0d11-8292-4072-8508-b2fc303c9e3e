import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';

interface Company {
  name: string;
  logo: string;
  industry: string;
}

interface PrRelease {
  id: string;
  company: Company;
  type: string;
  title: string;
  description: string;
  tags: string[];
  postedAt: string;
  deadline: string;
  isNew: boolean;
  fullContent?: string;
}

@Component({
  selector: 'app-pr-release-detail',
  templateUrl: './pr-release-detail.component.html',
  styleUrls: ['./pr-release-detail.component.css']
})
export class PrReleaseDetailComponent implements OnInit {
  release: PrRelease | undefined;
  loading = true;
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id');
    // In a real application, you would fetch the data from a service
    // For now, we'll use mock data
    this.fetchReleaseDetails(id);
  }

  private fetchReleaseDetails(id: string | null) {
    if (!id) {
      this.error = true;
      this.loading = false;
      return;
    }

    // Simulating API call
    setTimeout(() => {
      this.release = {
        id: id,
        company: {
          name: 'TechPulse',
          logo: 'assets/images/eco-ventures.png',
          industry: 'Technology'
        },
        type: 'PR RELEASE',
        title: 'TechPulse Launches Revolutionary AI-Powered Email Assistant',
        description: 'TechPulse has introduced a groundbreaking AI assistant for emails aimed at productivity-focused teams...',
        tags: ['AI', 'PRODUCTIVITY', 'TECHNEWS'],
        postedAt: 'An hour ago',
        deadline: '25 May 2025',
        isNew: true,
        fullContent: `TechPulse, a leading innovator in workplace productivity solutions, today announced the launch of its revolutionary AI-powered email assistant. This groundbreaking tool represents a significant leap forward in email management technology, promising to transform how professionals handle their daily communication.

The new AI assistant, dubbed "EmailPulse," leverages advanced machine learning algorithms to help users manage their inbox more efficiently. Key features include:

• Intelligent email prioritization
• Automated response suggestions
• Smart scheduling integration
• Context-aware follow-up reminders
• Multi-language support

Early beta testing has shown that EmailPulse can reduce email processing time by up to 60%, allowing professionals to focus on more strategic tasks. The tool has been particularly well-received in industries with high email volumes, such as consulting, legal services, and customer support.

"We're excited to bring this transformative technology to market," said Sarah Chen, CEO of TechPulse. "EmailPulse represents our commitment to making workplace communication more efficient and less stressful for professionals worldwide."

The product will be available for enterprise customers starting next month, with plans for a broader release to small businesses and individual users in Q3 2024.`
      };
      this.loading = false;
    }, 1000);
  }

  goBack() {
    this.location.back();
  }

  applyToRelease() {
    // Implement apply logic
    console.log('Applying to release:', this.release?.id);
  }

  shareRelease() {
    // Implement share logic
    console.log('Sharing release:', this.release?.id);
  }

  saveRelease() {
    // Implement save logic
    console.log('Saving release:', this.release?.id);
  }
}
