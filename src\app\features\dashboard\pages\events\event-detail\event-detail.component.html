<div class="min-h-screen bg-gray-50">
  <!-- Back Navigation -->
  <div class="bg-white pb-6 pt-2">
    <div class="max-w-7xl mx-auto px-4">
      <div class="py-4">
        <a
          routerLink="/dashboard/events"
          class="inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          <span>Eco Ventures Unveils Sustainable Packaging Solutions</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto border-2 rounded-xl">
    <div class="">
      <!-- Left Column - Event Details -->
      <img
        [src]="event.bannerImage"
        [alt]="event.title"
        class="w-full h-3/4 object-cover rounded-t-xl"
      />
      <div class="p-6">
        <div class="max-w-7xl mx-auto">
          <h1 class="text-2xl font-bold mb-4">{{ event.title }}</h1>
        </div>
        <!-- Event Info -->
        <div class="">
          <!-- Date and Time -->
          <div class="flex items-center gap-2 text-gray-600 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon icon-tabler icons-tabler-outline icon-tabler-calendar-week"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path
                d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z"
              />
              <path d="M16 3v4" />
              <path d="M8 3v4" />
              <path d="M4 11h16" />
              <path d="M7 14h.013" />
              <path d="M10.01 14h.005" />
              <path d="M13.01 14h.005" />
              <path d="M16.015 14h.005" />
              <path d="M13.015 17h.005" />
              <path d="M7.01 17h.005" />
              <path d="M10.01 17h.005" />
            </svg>
            <span>{{ event.date }}</span>
          </div>

          <!-- Location -->
          <div class="flex items-start gap-2 text-gray-600 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mt-0.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span>{{ event.location }}</span>
          </div>

          <!-- Event URL -->
          <div class="flex items-center gap-2 text-gray-600 mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path
                d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6"
              />
              <path d="M11 13l9 -9" />
              <path d="M15 4h5v5" />
            </svg>
            <a [href]="event.eventUrl" target="_blank" class="">{{
              event.eventUrl
            }}</a>
          </div>

          <!-- Right Column - Action Card -->
          <div class="">
            <div class="top-6">
              <!-- Attendees -->
              <div class="flex items-center gap-2 text-gray-600 mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon icon-tabler icons-tabler-outline icon-tabler-users"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" />
                  <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  <path d="M21 21v-2a4 4 0 0 0 -3 -3.85" />
                </svg>
                <span>{{ event.attendees }} attendees</span>
              </div>

              <!-- Speakers -->
              <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Speaker</h3>
                <div class="space-y-3">
                  <div
                    *ngFor="let speaker of event.speakers"
                    class="flex items-center gap-3"
                  >
                    <div
                      class="w-10 h-10 rounded-full text-center items-center flex justify-center object-cover bg-subtitlelight"
                    >
                      d
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">
                        Emma Wilsom
                      </h4>
                      <p class="text-xs text-subtitle">CEO, Future Tech</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div class="flex flex-wrap gap-2 mb-6">
                <span *ngFor="let tag of event.tags" class="badge">
                  {{ tag }}
                </span>
              </div>

              <!-- Speakers -->
              <div class="mb-6">
                <div class="space-y-3">
                  <div
                    *ngFor="let speaker of event.speakers"
                    class="flex items-center gap-3"
                  >
                    <img
                      [src]="speaker.image"
                      [alt]="speaker.name"
                      class="w-8 h-8 rounded-lg object-cover"
                    />
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">
                        {{ speaker.name }}
                      </h4>
                      <p class="text-xs text-gray-500">{{ speaker.title }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center gap-2">
                <button
                  (click)="attendEvent()"
                  class="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:bg-[#FF3D7D] transition-colors text-center"
                >
                  Attend Event
                </button>
                <div class="flex items-center gap-1">
                  <button
                    class="p-2 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                      />
                      <path d="M16 19h6" />
                      <path d="M19 16v6" />
                    </svg>
                  </button>
                  <button
                    class="p-2 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-share-3"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M13 4v4c-6.575 1.028 -9.02 6.788 -10 12c-.037 .206 5.384 -5.962 10 -6v4l8 -7l-8 -7z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-6 py-4 border-2 rounded-xl mt-6 mb-12">
    <div class="">
      <div class="whitespace-pre-line">{{ event.description }}</div>
    </div>
  </div>
</div>
