import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  User,
  UserCredentials,
  RegistrationData,
  AuthResponse,
  Role,
} from '../../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser$: Observable<User | null>;
  private tokenKey = 'auth_token';
  private userKey = 'current_user';

  // Mock users for testing
  private mockUsers: { [key: string]: User } = {
    '<EMAIL>': {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      roles: ['ADMIN'],
    },
    '<EMAIL>': {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Regular',
      lastName: 'User',
      roles: ['USER'],
    },
  };

  private mockPasswords: { [key: string]: string } = {
    '<EMAIL>': 'admin123',
    '<EMAIL>': 'user123',
  };

  constructor(private http: HttpClient) {
    this.currentUserSubject = new BehaviorSubject<User | null>(
      this.getUserFromStorage()
    );
    this.currentUser$ = this.currentUserSubject.asObservable();
  }

  // Get current user value
  public getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    const token = localStorage.getItem(this.tokenKey);
    return !!token; // Returns true if token exists
  }

  // Mock login implementation
  public login(credentials: UserCredentials): Observable<User> {
    const { email, password } = credentials;
    const mockUser = this.mockUsers[email];
    const correctPassword = this.mockPasswords[email];

    if (mockUser && password === correctPassword) {
      const response: AuthResponse = {
        user: mockUser,
        token: 'mock-jwt-token',
      };
      this.handleAuthentication(response);
      return of(mockUser);
    }

    return throwError(() => new Error('Invalid email or password'));
  }

  // Register
  public register(data: RegistrationData): Observable<User> {
    // For now, just return error as registration is not implemented
    return throwError(() => new Error('Registration is not available at the moment'));
  }

  // Logout
  public logout(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    this.currentUserSubject.next(null);
  }

  // Get auth token
  public getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  // Handle authentication response
  private handleAuthentication(response: AuthResponse): void {
    const { user, token } = response;

    // Store token and user in local storage
    localStorage.setItem(this.tokenKey, token);
    localStorage.setItem(this.userKey, JSON.stringify(user));

    // Update current user subject
    this.currentUserSubject.next(user);
  }

  // Get user from storage
  private getUserFromStorage(): User | null {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }
}
