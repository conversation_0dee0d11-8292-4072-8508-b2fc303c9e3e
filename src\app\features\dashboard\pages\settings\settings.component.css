/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(90deg, #F42346 0%, #F5327D 56.29%);
}

/* Focus styles */
input:focus, textarea:focus {
  box-shadow: 0 0 0 2px rgba(11, 1, 61, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-card {
    flex-direction: column;
  }
}

/* Smooth transitions */
button {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
