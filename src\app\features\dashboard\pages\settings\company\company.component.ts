import { Component } from '@angular/core';
import { Router } from '@angular/router';

interface User {
  name: string;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}

@Component({
  selector: 'app-company',
  templateUrl: './company.component.html',
  styleUrls: ['./company.component.css']
})


export class CompanyComponent {

  activeTab: 'overview' | 'credit-activity' | 'invoices' = 'overview';

  constructor(private router: Router) { }

  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png'
  };
  ngOnInit(): void {
    // Initialize component data
  }
  setActiveTab(tab: 'overview' | 'credit-activity' | 'invoices'): void {
    this.activeTab = tab;
  }
  events=[1,2]
  TeamMembers=[1,2,3,4,5,6]

}
