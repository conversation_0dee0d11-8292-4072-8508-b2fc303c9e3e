<!-- Main Layout -->
<div class="flex flex-col h-screen">
  <!-- Header -->
  <header class="bg-[#06021E] text-white fixed w-full top-0 z-50">
    <div class="px-4 md:px-6 py-3">
      <!-- Desktop Header -->
      <div class="hidden md:flex items-center justify-between">
        <!-- Logo and Navigation -->
        <div class="flex items-center space-x-8">
          <a href="/" class="text-[#FF4D8D] text-xl font-bold"><img src="assets/logo.png" alt="COMMSCLUB" class="w-auto h-auto" />
          </a>
          
          <!-- Desktop Navigation -->
          <nav class="flex items-center space-x-6">
            <a routerLink="/dashboard/pr-releases" routerLinkActive="text-[#FF4D8D]" class="hover:text-[#FF4D8D] transition-colors">PR Releases</a>
            <a routerLink="/dashboard/jobs" routerLinkActive="text-[#FF4D8D]" class="hover:text-[#FF4D8D] transition-colors">Jobs</a>
            <a routerLink="/dashboard/events" routerLinkActive="text-[#FF4D8D]" class="hover:text-[#FF4D8D] transition-colors">Events</a>
            <a routerLink="/dashboard/media-database" routerLinkActive="text-[#FF4D8D]" class="hover:text-[#FF4D8D] transition-colors">Media Database</a>
            <a routerLink="/dashboard/my-pitches" routerLinkActive="text-[#FF4D8D]" class="hover:text-[#FF4D8D] transition-colors">My Pitches</a>
          </nav>
        </div>

        <!-- Desktop Right Section -->
        <div class="flex items-center space-x-4">
          <!-- Credits -->
          <div class="flex items-center bg-[#3A2F7D] rounded-full px-4 py-2">
            <span class="mr-2 text-sm">CREDITS LEFT</span>
            <span class="bg-[#FF4D8D] px-3 py-1 rounded-full text-sm">500</span>
          </div>

          <!-- Notifications -->
          <button class="relative p-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <span class="absolute top-0 right-0 bg-red-500 rounded-full w-2 h-2"></span>
          </button>

          <!-- Desktop User Profile -->
          <div class="relative user-dropdown">
            <button 
              (click)="toggleDropdown()" 
              class="flex items-center space-x-2"
            >
              <img 
                src="assets/images/avatar.png" 
                alt="Profile" 
                class="w-8 h-8 rounded-full"
              >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            <!-- Dropdown Menu -->
            <div *ngIf="isDropdownOpen" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
              <a routerLink="/dashboard/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
              <button (click)="logout()" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile Header -->
      <div class="flex md:hidden items-center justify-between">
        <a href="/" class="text-[#FF4D8D] text-xl font-bold">  <a href="/" class="text-[#FF4D8D] text-xl font-bold"><img src="assets/logo.png" alt="COMMSCLUB" class="w-auto h-auto" />
        </a></a>
        
        <!-- Mobile Menu Button -->
        <button (click)="toggleMobileMenu()" class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>

      <!-- Mobile Menu -->
      <div *ngIf="isMobileMenuOpen" class="md:hidden">
        <nav class="mt-4 space-y-2">
          <a routerLink="/dashboard/pr-releases" routerLinkActive="text-[#FF4D8D]" 
            class="block py-2 hover:text-[#FF4D8D] transition-colors">PR Releases</a>
          <a routerLink="/dashboard/jobs" routerLinkActive="text-[#FF4D8D]" 
            class="block py-2 hover:text-[#FF4D8D] transition-colors">Jobs</a>
          <a routerLink="/dashboard/events" routerLinkActive="text-[#FF4D8D]" 
            class="block py-2 hover:text-[#FF4D8D] transition-colors">Events</a>
          <a routerLink="/dashboard/media-database" routerLinkActive="text-[#FF4D8D]" 
            class="block py-2 hover:text-[#FF4D8D] transition-colors">Media Database</a>
          <a routerLink="/dashboard/my-pitches" routerLinkActive="text-[#FF4D8D]" 
            class="block py-2 hover:text-[#FF4D8D] transition-colors">My Pitches</a>
        </nav>

        <!-- Mobile Credits and Profile -->
        <div class="mt-4 space-y-4 border-t border-gray-700 pt-4">
          <div class="flex items-center bg-[#3A2F7D] rounded-full px-4 py-2">
            <span class="mr-2 text-sm">CREDITS LEFT</span>
            <span class="bg-[#FF4D8D] px-3 py-1 rounded-full text-sm">500</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <img [src]="userImage || 'assets/images/default-avatar.png'" alt="Profile" class="w-8 h-8 rounded-full">
              <span class="text-sm">{{ userName }}</span>
            </div>
            <button (click)="logout()" class="text-red-500 text-sm">Logout</button>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="flex-1 overflow-auto bg-white pt-[60px] md:pt-[64px]">
    <router-outlet></router-outlet>
  </main>
</div>
