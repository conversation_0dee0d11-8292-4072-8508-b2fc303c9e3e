/* Billing settings specific styles */

.settings-container {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
}

/* Header Styles */
.settings-header {
  display: flex;
  align-items: center;
  padding: 1.25rem 2rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #111827;
  font-size: 1rem;
  font-weight: 500;
}

.back-button i {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Settings Content */
.settings-content {
  /* max-width: 800px; */
  margin: 0 auto;
  /* padding: 2rem; */
}

/* Tabs Navigation */
.tabs-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  /* border-bottom: 1px solid #E5E7EB; */
  background-color: #ededed;
  padding: 4px;
  border-radius: 12px;
}

.tab-item {
  padding: 4px 16px;
  font-size: 0.875rem;
  color: #6b7280;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-item.active {
  color: #fff;
  /* border-bottom: 2px solid #4F46E5; */
  font-weight: 500;
  background: #0b013d;
  border-radius: 12px;
}

/* Credits Card */
.credits-card {
  background-color: #ff4d8d;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  color: #ffffff;
}

.credits-header {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.credits-value {
  font-size: 2rem;
  font-weight: 600;
}

.credits-label {
  font-size: 0.875rem;
}

.credits-description {
  font-size: 0.875rem;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
  opacity: 0.9;
}

.add-credits-btn {
  background-color: #ffffff;
  color: #ff4d8d;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-credits-btn:hover {
  background-color: #f3f4f6;
}

/* Info Cards */
.info-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  /* padding: 1.5rem; */
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
}

.edit-button {
  padding: 0.2rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-button:hover {
  background-color: #e5e7eb;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #777777;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

/* Payment Methods */
.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.add-payment-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #ffffff;
  background-color: #ff4d8d;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-payment-button:hover {
  background-color: #e31b60;
}

.payment-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.payment-info {
  display: flex;
  gap: 2rem;
}

.payment-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
}

.detail-value {
  font-size: 0.875rem;
  color: #111827;
}

.delete-button {
  color: #ef4444;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-button:hover {
  background-color: #fee2e2;
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
