<section
  class="w-full flex flex-col lg:flex-row items-center justify-around py-6 md:py-10 bg-white min-h-[700px] relative px-4 md:px-8 gap-8"
>
  <!-- Left Column (Images + Text) -->
  <div
    class="w-full lg:w-1/2 flex flex-col items-start text-left order-1 max-md:order2"
  >
    <h3 class="px-2 md:px-6 text-[24px] md:text-[30px] leading-[1.2] mb-4">
      Amplify Your Presence with CommsClub
    </h3>
    <div class="space-y-4 md:space-y-6 p-2 md:p-6 font-[Inter]">
      <div
        *ngFor="let highlight of highlights"
        class="flex flex-col md:flex-row items-start md:items-center gap-4 md:gap-6"
      >
        <img
          [src]="highlight.image"
          [alt]="highlight.title"
          class="w-auto h-auto"
        />
        <div class="flex flex-col gap-2">
          <p class="leading-none tracking-normal text-[18px] md:text-[20px]">
            {{ highlight.title }}
          </p>
          <p class="text-base" [ngClass]="highlight.textLeading">
            {{ highlight.description }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column (Form) -->
  <div class="w-full lg:w-1/2 order-2 max-md:order-1">
    <app-form></app-form>
  </div>
</section>
