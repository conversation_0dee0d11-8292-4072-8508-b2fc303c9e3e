/* Primary Colors */
.bg-gradient-primary {
  background: linear-gradient(90deg, #FF4D8D 0%, #FF3D7D 100%);
}

.border-primary {
  border-color: #FF4D8D;
}

.text-primary {
  color: #FF4D8D;
}

/* Button Styles */
.btn-primary {
  padding: 0.5rem 1.5rem;
  background-color: #4f46e5;
  color: white;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: #4338ca;
}

/* Content Styles */
.prose {
  color: #374151;
  max-width: 65ch;
  line-height: 1.75;
}

.prose h2 {
  color: #111827;
  font-weight: 600;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
  list-style-type: disc;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

/* Tag Styles */
.tag {
  background-color: #f3f4f6;
  color: #4b5563;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.tag:hover {
  background-color: #e5e7eb;
}

/* Button Hover Effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Sticky sidebar behavior */
.sticky {
  position: sticky;
  top: 1.5rem;
}

/* Hover effects */
.hover\:bg-\[\#FF3D7D\]:hover {
  background-color: #FF3D7D;
}

.hover\:underline:hover {
  text-decoration: underline;
} 